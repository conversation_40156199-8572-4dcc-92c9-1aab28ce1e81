# 服务层迁移文档

## 迁移概述

本次迁移将express-self-tool项目中的服务层完整迁移到handle-ams-data项目中，实现了从Express.js到Next.js的架构转换。

## 迁移的组件

### 1. 配置文件
- **src/config/sjfClientConfig.ts** - SIF API客户端配置
- **src/config/performanceConfig.ts** - 性能监控配置

### 2. 服务类
- **src/services/sjfService.ts** - SIF API服务类
  - 单个ASIN数据获取
  - 批量ASIN数据获取
  - 缓存机制
  - 限流控制
  - 性能监控
- **src/services/excelProcessingService.ts** - Excel处理服务
  - Excel文件处理
  - ASIN数据Excel导出
  - 工作表平均值计算

### 3. API路由
- **src/app/api/sjf/list/route.ts** - ASIN数据获取API
- **src/app/api/sjf/stats/route.ts** - 性能统计API
- **src/app/api/sales/process-sales-excel/route.ts** - Excel处理API

### 4. 更新的文件
- **src/app/services/list.ts** - 更新为使用新的服务层

## API端点

### SJF API

#### POST /api/sjf/list
获取ASIN历史数据

**请求体:**
```json
{
  "country": "CA",
  "asin": "B0891XFDJ2" | ["B0891XFDJ2", "B08EXAMPLE1"],
  "date": "2024-01-01", // 可选
  "format": "json" | "excel" // 可选，批量请求默认excel，单个请求默认json
}
```

**响应:**
- JSON格式：返回处理后的数据
- Excel格式：返回Excel文件下载

#### GET /api/sjf/stats
获取性能统计信息

#### DELETE /api/sjf/stats
清除性能统计信息

### Sales API

#### POST /api/sales/process-sales-excel
处理销售Excel文件

**请求:**
- multipart/form-data
- 字段名：excelFile

**响应:**
- 处理后的Excel文件下载

## 功能特性

### 性能优化
- **缓存机制**: 5分钟内相同请求直接返回缓存
- **并发控制**: 最多8个并发请求
- **超时控制**: 20秒请求超时
- **性能监控**: 自动记录处理时间

### 批量处理
- 支持最多15个ASIN的批量请求
- 智能格式选择（批量默认Excel，单个默认JSON）
- 部分失败处理

### Excel功能
- 多工作表Excel生成（每个ASIN一个工作表）
- 自动列宽调整
- 样式格式化
- 平均值计算

## 测试

访问 `/test-migration` 页面进行功能测试：
- 单个ASIN测试
- 批量ASIN测试
- 性能统计测试

## 依赖要求

需要安装以下依赖包：
```bash
npm install exceljs dayjs
npm install --save-dev @types/node
```

### 快速安装
运行提供的安装脚本：
```bash
# Linux/Mac
chmod +x install-dependencies.sh
./install-dependencies.sh

# Windows
install-dependencies.bat
```

### 安装后的手动步骤
安装依赖后，需要更新 `src/services/excelProcessingService.ts` 文件：
1. 取消注释第2行：`import ExcelJS from 'exceljs';`
2. 删除第5行的临时类型定义：`declare const ExcelJS: any;`

## 配置说明

### 性能配置 (src/config/performanceConfig.ts)
- `REQUEST_TIMEOUT`: 请求超时时间（20秒）
- `MAX_CONCURRENT_REQUESTS`: 最大并发请求数（8个）
- `REQUEST_DELAY`: 请求间延迟（50ms）
- `CACHE_TTL`: 缓存生存时间（5分钟）
- `MAX_BATCH_SIZE`: 最大批量处理大小（15个）

### API配置 (src/config/sjfClientConfig.ts)
- API基础URL
- 认证令牌
- 默认请求头

## 错误处理

- **400**: 参数错误
- **422**: API返回非成功状态
- **500**: Excel生成失败或内部错误
- **502**: 上游API请求失败
- **207**: 部分成功（批量请求中部分失败）

## 迁移完成状态

✅ 配置文件迁移完成
✅ 核心服务类迁移完成
✅ API路由创建完成
✅ 现有服务文件更新完成
✅ 测试页面创建完成

## 后续步骤

1. 安装必要的依赖包（exceljs, dayjs）
2. 运行测试验证功能
3. 根据需要调整配置参数
4. 部署到生产环境
