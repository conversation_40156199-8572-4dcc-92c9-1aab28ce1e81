// 性能配置文件
export const PERFORMANCE_CONFIG = {
  // HTTP 请求配置
  REQUEST_TIMEOUT: 20000, // 20秒超时（增加到20秒）
  MAX_CONCURRENT_REQUESTS: 8, // 最大并发请求数（增加到8个）
  REQUEST_DELAY: 50, // 请求间延迟（毫秒，增加到50ms避免过于频繁）

  // 缓存配置
  CACHE_TTL: 5 * 60 * 1000, // 5分钟缓存
  MAX_CACHE_SIZE: 1000, // 最大缓存条目数

  // 性能监控配置
  SLOW_REQUEST_THRESHOLD: 5000, // 慢请求阈值（毫秒，调整到5秒）
  ENABLE_PERFORMANCE_LOGGING: true,

  // 批量处理配置
  MAX_BATCH_SIZE: 15, // 最大批量处理大小（增加到15）
  BATCH_PROCESSING_TIMEOUT: 60000, // 批量处理超时（增加到60秒）
};

// 性能监控工具
export class PerformanceMonitor {
  private static metrics: Map<string, number[]> = new Map();
  
  static startTimer(operation: string): () => number {
    const startTime = Date.now();
    
    return () => {
      const duration = Date.now() - startTime;
      this.recordMetric(operation, duration);
      return duration;
    };
  }
  
  static recordMetric(operation: string, duration: number): void {
    if (!this.metrics.has(operation)) {
      this.metrics.set(operation, []);
    }
    
    const metrics = this.metrics.get(operation)!;
    metrics.push(duration);
    
    // 保持最近100个记录
    if (metrics.length > 100) {
      metrics.shift();
    }
    
    // 记录慢请求
    if (duration > PERFORMANCE_CONFIG.SLOW_REQUEST_THRESHOLD) {
      console.warn(`[Performance] Slow operation detected: ${operation} took ${duration}ms`);
    }
  }
  
  static getStats(operation: string): { avg: number; min: number; max: number; count: number } | null {
    const metrics = this.metrics.get(operation);
    if (!metrics || metrics.length === 0) {
      return null;
    }
    
    const avg = metrics.reduce((sum, val) => sum + val, 0) / metrics.length;
    const min = Math.min(...metrics);
    const max = Math.max(...metrics);
    
    return { avg: Math.round(avg), min, max, count: metrics.length };
  }
  
  static getAllStats(): Record<string, any> {
    const stats: Record<string, any> = {};
    
    for (const [operation] of this.metrics) {
      stats[operation] = this.getStats(operation);
    }
    
    return stats;
  }
  
  static clearStats(): void {
    this.metrics.clear();
  }
}
