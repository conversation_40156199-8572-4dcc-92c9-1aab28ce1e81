import { 
  Home, 
  BarChart3, 
  FileSpreadsheet, 
  Settings, 
  TestTube,
  Database,
  TrendingUp
} from 'lucide-react';

export interface NavigationItem {
  id: string;
  label: string;
  href: string;
  icon: any;
  description?: string;
  badge?: string;
  children?: NavigationItem[];
}

export const navigationConfig: NavigationItem[] = [
  {
    id: 'dashboard',
    label: '数据面板',
    href: '/',
    icon: Home,
    description: 'ASIN数据查询和分析'
  },
  {
    id: 'analytics',
    label: '数据分析',
    href: '/analytics',
    icon: BarChart3,
    description: '深度数据分析和图表',
    children: [
      {
        id: 'trends',
        label: '趋势分析',
        href: '/analytics/trends',
        icon: TrendingUp,
        description: '价格和排名趋势'
      },
      {
        id: 'comparison',
        label: '对比分析',
        href: '/analytics/comparison',
        icon: BarChart3,
        description: 'ASIN对比分析'
      }
    ]
  },
  {
    id: 'tools',
    label: '工具集',
    href: '/tools',
    icon: Database,
    description: 'Excel处理和批量操作',
    children: [
      {
        id: 'excel-processor',
        label: 'Excel处理',
        href: '/tools/excel',
        icon: FileSpreadsheet,
        description: 'Excel文件处理和导出'
      },
      {
        id: 'batch-query',
        label: '批量查询',
        href: '/tools/batch',
        icon: Database,
        description: '批量ASIN数据查询'
      }
    ]
  },
  {
    id: 'test',
    label: '测试中心',
    href: '/test-migration',
    icon: TestTube,
    description: '功能测试和调试',
    badge: 'Beta'
  },
  {
    id: 'settings',
    label: '设置',
    href: '/settings',
    icon: Settings,
    description: '系统设置和配置'
  }
];

// 获取扁平化的导航项（用于面包屑等）
export function getFlatNavigation(): NavigationItem[] {
  const flatItems: NavigationItem[] = [];
  
  function flatten(items: NavigationItem[]) {
    items.forEach(item => {
      flatItems.push(item);
      if (item.children) {
        flatten(item.children);
      }
    });
  }
  
  flatten(navigationConfig);
  return flatItems;
}

// 根据路径查找导航项
export function findNavigationItem(href: string): NavigationItem | undefined {
  const flatItems = getFlatNavigation();
  return flatItems.find(item => item.href === href);
}

// 检查路径是否激活（包括子路径）
export function isPathActive(itemHref: string, currentPath: string): boolean {
  if (itemHref === '/') {
    return currentPath === '/';
  }
  return currentPath.startsWith(itemHref);
}
