// src/config/sjfClientConfig.ts

// 从 Express 项目迁移的服务逻辑常量
export const API_BASE_URL = "https://www.sif.com/api/extension/search/asinHistoryData";
export const AUTHORIZATION_TOKEN =
  "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ3ZWNoYXRpZCI6Im90SkwwNTd6eTJaQkJ1SE5EOU9GT0prdFhJMmsiLCJ1c2VyU2FsdCI6InpOMkxZSjBmIiwiZXhwIjoxNzUwNjQ2MDEwLCJ1c2VyaWQiOiI5aWVUNUtDVEFtczlGOTgxNTFPRWI3MDEiLCJwbGF0Zm9ybSI6ImV4dGVuc2lvbiJ9.mDy42BB2r-jmxCaBioxIaYEf_kHyArEV5_JRDfKOWFg";
export const COOKIE =
  "sif_token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ3ZWNoYXRpZCI6Im90SkwwNTd6eTJaQkJ1SE5EOU9GT0prdFhJMmsiLCJ1c2VyU2FsdCI6InpOMkxZSjBmIiwiZXhwIjoxNzUwNjQ2MDEwLCJ1c2VyaWQiOiI5aWVUNUtDVEFtczlGOTgxNTFPRWI3MDEiLCJwbGF0Zm9ybSI6ImV4dGVuc2lvbiJ9.mDy42BB2r-jmxCaBioxIaYEf_kHyArEV5_JRDfKOWFg";
export const SUFFIX_PARAMS = "&version=1.0.5&_t=1744337979679";

export const DEFAULT_HEADERS = {
  Host: "www.sif.com",
  "User-Agent":
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
  Authorization: `${AUTHORIZATION_TOKEN}`, // 使用导入的常量
  "Content-Type": "application/json",
  Cookie: `${COOKIE}`, // 使用导入的常量
};
