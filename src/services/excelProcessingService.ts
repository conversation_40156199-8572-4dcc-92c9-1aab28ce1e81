import ExcelJS from 'exceljs';

interface IExcelProcessingService {
  processExcelFile(fileBuffer: Buffer): Promise<Buffer>;
  createAsinDataExcel(batchResults: Array<{asin: string, data: any[]}>): Promise<Buffer>;
  processSheetAverages(fileBuffer: Buffer): Promise<Buffer>;
}

/**
 * Service for processing Excel files.
 */
class ExcelProcessingService implements IExcelProcessingService {
  /**
   * Processes an Excel file buffer to fill empty cells in columns with existing non-null values.
   * @param fileBuffer The buffer of the Excel file.
   * @returns A promise that resolves with the buffer of the processed Excel file.
   */
  public async processExcelFile(fileBuffer: Buffer): Promise<Buffer> {
    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.load(fileBuffer);

    workbook.eachSheet((worksheet) => {
      const columnCount = worksheet.columnCount;
      const rowCount = worksheet.rowCount;

      for (let col = 1; col <= columnCount; col++) {
        const column = worksheet.getColumn(col);
        const values = column.values as any[];

        // Find the first non-null value in the column
        let fillValue: any = null;
        for (let i = 1; i < values.length; i++) {
          if (values[i] !== null && values[i] !== undefined && values[i] !== '') {
            fillValue = values[i];
            break;
          }
        }

        // Fill empty cells with the found value
        if (fillValue !== null) {
          for (let row = 1; row <= rowCount; row++) {
            const cell = worksheet.getCell(row, col);
            if (cell.value === null || cell.value === undefined || cell.value === '') {
              cell.value = fillValue;
            }
          }
        }
      }
    });

    const buffer = await workbook.xlsx.writeBuffer();
    return Buffer.from(buffer);
  }

  /**
   * Creates an Excel file with ASIN data, each ASIN as a separate worksheet.
   * @param batchResults Array of ASIN results with data
   * @returns Promise<Buffer> Excel file buffer
   */
  public async createAsinDataExcel(batchResults: Array<{asin: string, data: any[]}>): Promise<Buffer> {
    const workbook = new ExcelJS.Workbook();
    
    // 设置工作簿属性
    workbook.creator = 'SJF API Service';
    workbook.lastModifiedBy = 'SJF API Service';
    workbook.created = new Date();
    workbook.modified = new Date();

    for (const result of batchResults) {
      if (!result.data || result.data.length === 0) {
        // 为没有数据的ASIN创建空工作表
        const worksheet = workbook.addWorksheet(result.asin);
        worksheet.addRow(['No data available for this ASIN']);
        continue;
      }

      // 为每个ASIN创建工作表
      const worksheet = workbook.addWorksheet(result.asin);
      
      // 获取数据的所有键作为列标题
      const allKeys = new Set<string>();
      result.data.forEach(item => {
        Object.keys(item).forEach(key => allKeys.add(key));
      });
      
      const headers = Array.from(allKeys);
      
      // 添加标题行
      worksheet.addRow(headers);
      
      // 设置标题行样式
      const headerRow = worksheet.getRow(1);
      headerRow.font = { bold: true };
      headerRow.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFE0E0E0' }
      };
      
      // 添加数据行
      result.data.forEach(item => {
        const row = headers.map(header => item[header] || '');
        worksheet.addRow(row);
      });
      
      // 自动调整列宽
      headers.forEach((header, index) => {
        const column = worksheet.getColumn(index + 1);
        let maxLength = header.length;
        
        result.data.forEach(item => {
          const value = item[header];
          if (value !== null && value !== undefined) {
            const length = value.toString().length;
            if (length > maxLength) {
              maxLength = length;
            }
          }
        });
        
        column.width = Math.min(maxLength + 2, 50); // 最大宽度50
      });
    }

    // 如果没有任何工作表，创建一个默认的
    if (workbook.worksheets.length === 0) {
      const worksheet = workbook.addWorksheet('No Data');
      worksheet.addRow(['No data available']);
    }

    const buffer = await workbook.xlsx.writeBuffer();
    return Buffer.from(buffer);
  }

  /**
   * Processes Excel file to calculate averages for each sheet.
   * @param fileBuffer The buffer of the Excel file.
   * @returns A promise that resolves with the buffer of the processed Excel file.
   */
  public async processSheetAverages(fileBuffer: Buffer): Promise<Buffer> {
    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.load(fileBuffer);

    workbook.eachSheet((worksheet) => {
      const rowCount = worksheet.rowCount;
      const columnCount = worksheet.columnCount;

      // Skip if worksheet is empty
      if (rowCount === 0 || columnCount === 0) return;

      // Calculate averages for each column (assuming first row is headers)
      for (let col = 1; col <= columnCount; col++) {
        const _column = worksheet.getColumn(col);
        const values: number[] = [];

        // Collect numeric values from column (skip header row)
        for (let row = 2; row <= rowCount; row++) {
          const cell = worksheet.getCell(row, col);
          const value = cell.value;
          
          if (typeof value === 'number' && !isNaN(value)) {
            values.push(value);
          }
        }

        // Calculate and add average if we have numeric values
        if (values.length > 0) {
          const average = values.reduce((sum, val) => sum + val, 0) / values.length;
          const avgRow = worksheet.addRow();
          avgRow.getCell(col).value = parseFloat(average.toFixed(2));
          
          // Add label in first column if this is the first column
          if (col === 1) {
            avgRow.getCell(1).value = 'Average';
            avgRow.font = { bold: true };
          }
        }
      }
    });

    const buffer = await workbook.xlsx.writeBuffer();
    return Buffer.from(buffer);
  }
}

export default new ExcelProcessingService();
