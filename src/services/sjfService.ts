// src/services/sjfService.ts
import {
  API_BASE_URL,
  DEFAULT_HEADERS,
  SUFFIX_PARAMS,
} from '../config/sjfClientConfig'; // 导入配置
import { PERFORMANCE_CONFIG, PerformanceMonitor } from '../config/performanceConfig';

// 简单的内存缓存
interface CacheEntry {
  data: any;
  timestamp: number;
}

const cache = new Map<string, CacheEntry>();

/**
 * @description SIF 服务类，封装与 SIF API 交互的逻辑
 */
class SjfService {
  private requestQueue: Array<() => Promise<any>> = [];
  private activeRequests = 0;
  private readonly maxConcurrentRequests = PERFORMANCE_CONFIG.MAX_CONCURRENT_REQUESTS;

  /**
   * @description 生成缓存键
   */
  private getCacheKey(country: string, asin: string): string {
    return `${country}-${asin}`;
  }

  /**
   * @description 检查缓存是否有效
   */
  private isCacheValid(entry: CacheEntry): boolean {
    return Date.now() - entry.timestamp < PERFORMANCE_CONFIG.CACHE_TTL;
  }

  /**
   * @description 从缓存获取数据
   */
  private getFromCache(country: string, asin: string): any | null {
    const key = this.getCacheKey(country, asin);
    const entry = cache.get(key);

    if (entry && this.isCacheValid(entry)) {
      return entry.data;
    }

    // 清理过期缓存
    if (entry) {
      cache.delete(key);
    }

    return null;
  }

  /**
   * @description 将数据存入缓存
   */
  private setCache(country: string, asin: string, data: any): void {
    const key = this.getCacheKey(country, asin);
    cache.set(key, {
      data,
      timestamp: Date.now()
    });
  }

  /**
   * @description 限流执行请求（改进版本，支持超时和重试）
   */
  private async executeWithRateLimit<T>(requestFn: () => Promise<T>, maxRetries: number = 2): Promise<T> {
    return new Promise((resolve, reject) => {
      const startTime = Date.now();
      const maxWaitTime = PERFORMANCE_CONFIG.BATCH_PROCESSING_TIMEOUT; // 最大等待时间

      const execute = async (retryCount: number = 0) => {
        // 检查是否等待时间过长
        if (Date.now() - startTime > maxWaitTime) {
          reject(new Error(`Request timeout: waited too long in queue (${maxWaitTime}ms)`));
          return;
        }

        if (this.activeRequests >= this.maxConcurrentRequests) {
          // 如果达到并发限制，加入队列
          this.requestQueue.push(() => execute(retryCount));
          return;
        }

        this.activeRequests++;
        try {
          const result = await requestFn();
          resolve(result);
        } catch (error) {
          // 如果是超时错误且还有重试次数，则重试
          if (error instanceof Error &&
              error.name === 'AbortError' &&
              retryCount < maxRetries) {
            console.warn(`Request aborted, retrying (${retryCount + 1}/${maxRetries})...`);
            // 等待一下再重试
            setTimeout(() => execute(retryCount + 1), 1000 * (retryCount + 1));
          } else {
            reject(error);
          }
        } finally {
          this.activeRequests--;
          // 处理队列中的下一个请求
          if (this.requestQueue.length > 0) {
            const nextRequest = this.requestQueue.shift();
            if (nextRequest) {
              setTimeout(nextRequest, PERFORMANCE_CONFIG.REQUEST_DELAY);
            }
          }
        }
      };

      execute();
    });
  }

  /**
   * @description 从 SIF API 获取 ASIN 历史数据
   * @param country 国家代码，例如 "CA"
   * @param asin ASIN 值，例如 "B0891XFDJ2"
   * @returns Promise<any> API 返回的数据
   */
  public async getAsinHistory(country: string = "CA", asin: string = "B0891XFDJ2"): Promise<any> {
    const timer = PerformanceMonitor.startTimer(`getAsinHistory-${country}-${asin}`);

    try {
      // 首先检查缓存
      const cachedData = this.getFromCache(country, asin);
      if (cachedData) {
        console.log(`Cache hit for ${country}-${asin}`);
        timer(); // 记录缓存命中时间
        return cachedData;
      }

      // 使用限流执行请求
      return await this.executeWithRateLimit(async () => {
        try {
          const url = `${API_BASE_URL}?country=${country}&asin=${asin}${SUFFIX_PARAMS}`;

          // 添加超时控制
          const controller = new AbortController();
          const timeoutId = setTimeout(() => controller.abort(), PERFORMANCE_CONFIG.REQUEST_TIMEOUT);

          const response = await fetch(url, {
            method: "GET",
            headers: DEFAULT_HEADERS,
            signal: controller.signal,
          });

          clearTimeout(timeoutId);

          if (!response.ok) {
            throw new Error(`SIF API request failed with status ${response.status}`);
          }

          const data = await response.json();

          // 存入缓存
          this.setCache(country, asin, data);

          const duration = timer(); // 记录请求时间
          console.log(`API request for ${country}-${asin} completed in ${duration}ms`);

          return data;
        } catch (error) {
          timer(); // 即使出错也记录时间
          console.error(`Error fetching ASIN history for ${country}-${asin}:`, error);
          throw error;
        }
      });
    } catch (error) {
      timer(); // 确保总是记录时间
      throw error;
    }
  }

  /**
   * @description 批量获取多个 ASIN 的历史数据（优化版本）
   * @param country 国家代码
   * @param asins ASIN 数组
   * @returns Promise<Array<{asin: string, data: any, error?: string}>>
   */
  public async getBatchAsinHistory(country: string, asins: string[]): Promise<Array<{asin: string, data: any, error?: string}>> {
    console.log(`Starting batch request for ${asins.length} ASINs: ${asins.join(', ')}`);

    const results = await Promise.allSettled(
      asins.map(async (asin, index) => {
        try {
          // 为每个请求添加一个小的延迟，避免同时发起太多请求
          if (index > 0) {
            await new Promise(resolve => setTimeout(resolve, index * 100));
          }

          const data = await this.getAsinHistory(country, asin);
          console.log(`✅ Successfully fetched data for ${asin}`);
          return { asin, data };
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          console.error(`❌ Failed to fetch data for ${asin}: ${errorMessage}`);

          return {
            asin,
            data: null,
            error: errorMessage
          };
        }
      })
    );

    const finalResults = results.map((result, index) => {
      if (result.status === 'fulfilled') {
        return result.value;
      } else {
        const errorMessage = result.reason?.message || 'Request failed';
        console.error(`❌ Promise rejected for ${asins[index]}: ${errorMessage}`);
        return {
          asin: asins[index],
          data: null,
          error: errorMessage
        };
      }
    });

    // 统计结果
    const successful = finalResults.filter(r => r.data !== null).length;
    const failed = finalResults.length - successful;
    console.log(`Batch request completed: ${successful} successful, ${failed} failed`);

    return finalResults;
  }
}

export default new SjfService();