/* Date Picker Custom Styles */
.rdp-custom {
  --rdp-cell-size: 32px;
  --rdp-accent-color: #3b82f6;
  --rdp-background-color: #ffffff;
  --rdp-accent-color-dark: #1d4ed8;
  --rdp-background-color-dark: #1f2937;
  --rdp-outline: 2px solid var(--rdp-accent-color);
  --rdp-outline-selected: 2px solid var(--rdp-accent-color);
  --rdp-selected-color: #ffffff;
}

.rdp-custom .rdp-months {
  display: flex;
}

.rdp-custom .rdp-month {
  margin: 0;
}

.rdp-custom .rdp-table {
  margin: 0;
  max-width: calc(var(--rdp-cell-size) * 7);
}

.rdp-custom .rdp-with_weeknumber .rdp-table {
  max-width: calc(var(--rdp-cell-size) * 8);
}

.rdp-custom .rdp-head_cell {
  vertical-align: middle;
  font-weight: 500;
  text-align: center;
  height: var(--rdp-cell-size);
  width: var(--rdp-cell-size);
  text-transform: uppercase;
  font-size: 0.75rem;
  color: #6b7280;
}

.rdp-custom .rdp-tbody {
  border: 0;
}

.rdp-custom .rdp-tfoot {
  margin: 0.5rem;
}

.rdp-custom .rdp-row {
  border: 0;
}

.rdp-custom .rdp-cell {
  width: var(--rdp-cell-size);
  height: var(--rdp-cell-size);
  text-align: center;
  font-size: 0.875rem;
  border: 0;
  border-radius: 6px;
  margin: 1px;
}

.rdp-custom .rdp-button {
  appearance: none;
  background: none;
  border: 0;
  color: inherit;
  cursor: pointer;
  font: inherit;
  outline: none;
  width: var(--rdp-cell-size);
  height: var(--rdp-cell-size);
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease-in-out;
}

.rdp-custom .rdp-button:hover {
  background-color: #dbeafe;
  color: #1e40af;
}

.rdp-custom .rdp-button:focus {
  outline: var(--rdp-outline);
  outline-offset: 2px;
  z-index: 1;
}

.rdp-custom .rdp-day_today {
  background-color: #dbeafe;
  color: #1e40af;
  font-weight: 600;
}

.rdp-custom .rdp-day_selected {
  background-color: var(--rdp-accent-color);
  color: var(--rdp-selected-color);
  font-weight: 600;
}

.rdp-custom .rdp-day_selected:hover {
  background-color: var(--rdp-accent-color-dark);
}

.rdp-custom .rdp-day_outside {
  color: #9ca3af;
  opacity: 0.5;
}

.rdp-custom .rdp-day_disabled {
  color: #d1d5db;
  opacity: 0.5;
  cursor: not-allowed;
}

.rdp-custom .rdp-day_disabled:hover {
  background-color: transparent;
  color: #d1d5db;
}

.rdp-custom .rdp-nav {
  display: flex;
  align-items: center;
}

.rdp-custom .rdp-nav_button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  border-radius: 0.375rem;
  border: 0;
  background: transparent;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
}

.rdp-custom .rdp-nav_button:hover {
  background-color: #f3f4f6;
  color: #374151;
}

.rdp-custom .rdp-nav_button:focus {
  outline: var(--rdp-outline);
  outline-offset: 2px;
}

.rdp-custom .rdp-nav_button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.rdp-custom .rdp-caption_label {
  font-weight: 600;
  color: #111827;
}

.rdp-custom .rdp-weeknumber {
  font-size: 0.75rem;
  font-weight: 500;
  color: #6b7280;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .rdp-custom {
    --rdp-background-color: var(--rdp-background-color-dark);
    --rdp-accent-color: #60a5fa;
    --rdp-accent-color-dark: #3b82f6;
  }
  
  .rdp-custom .rdp-day_today {
    background-color: #1e3a8a;
    color: #93c5fd;
  }
  
  .rdp-custom .rdp-button:hover {
    background-color: #1e3a8a;
    color: #93c5fd;
  }
  
  .rdp-custom .rdp-caption_label {
    color: #f9fafb;
  }
  
  .rdp-custom .rdp-head_cell {
    color: #9ca3af;
  }
  
  .rdp-custom .rdp-nav_button {
    color: #9ca3af;
  }
  
  .rdp-custom .rdp-nav_button:hover {
    background-color: #374151;
    color: #f3f4f6;
  }
}
