import  { Router, Request, Response, NextFunction } from 'express';
import multer from 'multer';
import ExcelProcessingService from '../services/excelProcessingService';



const router: Router = Router();
const upload = multer({ storage: multer.memoryStorage() });

router.post('/process-sales-excel', upload.single('excelFile'), async (req: Request & { file: Express.Multer.File }, res: Response, next: NextFunction) => {
  if (!req.file) {
    res.status(400).send({ message: 'No file uploaded.' });
    return;
  }

  try {   
    const originalFilename = req.file.originalname;
    const processedBuffer = await ExcelProcessingService.processSheetAverages(req.file.buffer);

    const timestamp = new Date().toISOString().replace(/[-:.]/g, '').slice(0, 14); // YYYYMMDDHHmmss
    const lastDotIndex = originalFilename.lastIndexOf('.');
    const baseFilename = lastDotIndex > -1 ? originalFilename.substring(0, lastDotIndex) : originalFilename;
    const outputFilename = `processed_${baseFilename}_${timestamp}.xlsx`;
    const encodedOutputFilename = encodeURIComponent(outputFilename);

    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', `attachment; filename*=UTF-8''${encodedOutputFilename}`);
    res.send(processedBuffer);
  } catch (error) {
    next(error); // 传递给全局错误处理中间件
  }
});


export default router;