import express, { Router, Request, Response } from 'express';

const router: Router = express.Router();

// 示例：获取所有用户
router.get('/', (req: Request, res: Response) => {
  res.json({ message: 'GET all users - from userRoutes' });
});

// 示例：获取单个用户
router.get('/:id', (req: Request, res: Response) => {
  res.json({ message: `GET user with ID ${req.params.id} - from userRoutes` });
});

// 示例：创建新用户
router.post('/', (req: Request, res: Response) => {
  res.status(201).json({ message: 'POST new user - from userRoutes', data: req.body });
});

export default router;