import express, { Request, Response, Application } from 'express';
import dayjs from 'dayjs';
import mainRouter from './routes'; // 导入聚合路由

const app: Application = express();
const port: number = process.env.PORT ? parseInt(process.env.PORT, 10) : 3000;

// 使用 JSON 解析中间件，以便处理 POST 请求的 body
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// 注册主路由，所有来自 './routes' 的路由都会挂载在 '/api' 路径下
app.use('/api', mainRouter);

// 可以保留一个根路径用于健康检查或API文档等
app.get('/', (req: Request, res: Response) => {
  res.send(`API Root - Express + TypeScript! Today is: ${dayjs().format('YYYY-MM-DD HH:mm:ss')}`);
});

app.listen(port, () => {
  console.log(`Server is running on http://localhost:${port}`);
});