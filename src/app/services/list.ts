"use server";

import { headers } from "next/headers";
import { NextResponse } from "next/server";
import { ReadableStream } from "stream/web";

const API_BASE_URL = "https://www.sif.com/api/extension/search/asinHistoryData";
const t = "&_t=1744337979679";
const authorization =
  "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ3ZWNoYXRpZCI6Im90SkwwNTd6eTJaQkJ1SE5EOU9GT0prdFhJMmsiLCJ1c2VyU2FsdCI6InpOMkxZSjBmIiwiZXhwIjoxNzUwNjQ2MDEwLCJ1c2VyaWQiOiI5aWVUNUtDVEFtczlGOTgxNTFPRWI3MDEiLCJwbGF0Zm9ybSI6ImV4dGVuc2lvbiJ9.mDy42BB2r-jmxCaBioxIaYEf_kHyArEV5_JRDfKOWFg";
const Cookie =
  "sif_token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ3ZWNoYXRpZCI6Im90SkwwNTd6eTJaQkJ1SE5EOU9GT0prdFhJMmsiLCJ1c2VyU2FsdCI6InpOMkxZSjBmIiwiZXhwIjoxNzUwNjQ2MDEwLCJ1c2VyaWQiOiI5aWVUNUtDVEFtczlGOTgxNTFPRWI3MDEiLCJwbGF0Zm9ybSI6ImV4dGVuc2lvbiJ9.mDy42BB2r-jmxCaBioxIaYEf_kHyArEV5_JRDfKOWFg";

const suffixParams = "&version=1.0.5&_t=1744337979679";

const defaultHeaders = {
  Host: "www.sif.com",
  "User-Agent":
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
  Authorization: `${authorization}`,
  "Content-Type": "application/json",
  Cookie: `${Cookie}`,
};

export async function list(
  country: string = "CA",
  asin: string = "B0891XFDJ2"
) {
  try {
    const url = `${API_BASE_URL}?country=${country}&asin=${asin}${suffixParams}`;
    const response = await fetch(url, {
      method: "GET",
      headers: {
        // 添加 Next.js 特定的请求头
        // ...Object.fromEntries(await headers()),
        ...defaultHeaders,
      },
    });

    if (!response.ok) {
      throw new Error(`API request failed with status ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Error sending message:", error);
    throw error;
  }
}
