"use server";

import SjfService from '@/services/sjfService';

/**
 * 获取单个ASIN的历史数据
 * @param country 国家代码，默认为 "CA"
 * @param asin ASIN值，默认为 "B0891XFDJ2"
 * @returns Promise<any> API返回的数据
 */
export async function list(
  country: string = "CA",
  asin: string = "B0891XFDJ2"
) {
  try {
    const data = await SjfService.getAsinHistory(country, asin);
    return data;
  } catch (error) {
    console.error("Error fetching ASIN data:", error);
    throw error;
  }
}

/**
 * 批量获取多个ASIN的历史数据
 * @param country 国家代码
 * @param asins ASIN数组
 * @returns Promise<Array<{asin: string, data: any, error?: string}>>
 */
export async function batchList(
  country: string,
  asins: string[]
) {
  try {
    const results = await SjfService.getBatchAsinHistory(country, asins);
    return results;
  } catch (error) {
    console.error("Error fetching batch ASIN data:", error);
    throw error;
  }
}
