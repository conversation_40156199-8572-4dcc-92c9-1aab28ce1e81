"use client";
import React, { useEffect, useState } from "react";
import { DatePicker } from "@/components/ui/date-picker";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import "@/styles/date-picker.css";

// 定义数据类型接口
interface AsinData {
  dates: string;
  subBsr: Record<string, number>;
  buyboxPrice?: number;
}

export default function Home() {
  const [dateValue, setDateValue] = useState<string>("");
  const [subBrsKeyOne, setSubBrsKeyOne] = useState<string>("");
  const [subBrsKeyTwo, setSubBrsKeyTwo] = useState<string>("");
  const [list, setList] = useState<AsinData[]>([]);
  const [asinValue, setAsinValue] = useState<string>("");
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    initial();
  }, []);

  // 处理ASIN输入，支持单个和多个
  const processAsinInput = (input: string) => {
    if (!input.trim()) return [];

    // 分割ASIN，支持换行、逗号、空格等分隔符
    const asins = input
      .split(/[\n,\s]+/)
      .map(asin => asin.trim())
      .filter(asin => asin.length > 0);

    return asins;
  };

  const initial = async () => {
    setError(null);

    if (!asinValue.trim()) {
      setError('请输入ASIN');
      return;
    }

    const asins = processAsinInput(asinValue);
    if (asins.length === 0) {
      setError('请输入有效的ASIN');
      return;
    }

    setLoading(true);
    try {
      // 暂时只处理第一个ASIN，后续可以扩展为批量处理
      const firstAsin = asins[0];

      if (asins.length > 1) {
        setError(`当前版本暂时只支持单个ASIN查询，正在查询: ${firstAsin}`);
      }

      const response = await fetch("/api/list", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          asin: firstAsin,
          date: dateValue
        }),
      });

      if (!response.ok) {
        throw new Error(`请求失败: ${response.status}`);
      }

      const { data }: { data: AsinData[] } = await response.json();

      if (data && data.length > 0) {
        const getOneData = data[0];
        const { subBsr } = getOneData;
        const keyOne = Object.keys(subBsr)?.[0] || '';
        const keyTwo = Object.keys(subBsr)?.[1] || '';
        setSubBrsKeyOne(keyOne);
        setSubBrsKeyTwo(keyTwo);
        setList(data);
      } else {
        setList([]);
        setError('未找到数据，请检查ASIN是否正确');
      }
    } catch (error) {
      console.log(error);
      setError('查询失败，请检查网络连接');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-8">
      {/* Page Header */}
      <div className="text-center md:text-left">
        <div className="inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-indigo-500 to-blue-600 text-white rounded-full text-sm font-medium mb-6 shadow-lg">
          <span className="w-2 h-2 bg-white/80 rounded-full animate-pulse"></span>
          Amazon数据分析平台
        </div>
        <h1 className="text-4xl md:text-5xl font-bold tracking-tight bg-gradient-to-r from-gray-900 via-gray-800 to-gray-700 bg-clip-text text-transparent mb-4">
          ASIN数据查询
        </h1>
        <p className="text-xl text-gray-600 max-w-3xl leading-relaxed">
          专业的Amazon产品数据分析平台，助您洞察市场趋势
        </p>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 flex items-center gap-3">
          <div className="w-5 h-5 bg-red-500 rounded-full flex items-center justify-center flex-shrink-0">
            <span className="text-white text-xs">!</span>
          </div>
          <div>
            <p className="text-red-800 font-medium">错误</p>
            <p className="text-red-600 text-sm">{error}</p>
          </div>
        </div>
      )}

      {/* Search Controls */}
      <div className="bg-white rounded-3xl border border-gray-100 shadow-xl overflow-hidden backdrop-blur-sm">
        <div className="bg-gradient-to-r from-indigo-500 via-blue-500 to-cyan-500 px-8 py-6">
          <h3 className="text-xl font-bold text-white flex items-center gap-3">
            <div className="w-8 h-8 bg-white/20 rounded-xl flex items-center justify-center backdrop-blur-sm">
              <span className="w-3 h-3 bg-white rounded-full"></span>
            </div>
            查询条件
          </h3>
          <p className="text-blue-100 text-sm mt-1">输入您要查询的ASIN信息</p>
        </div>
        <div className="p-8">
          <div className="space-y-8">
            {/* 日期选择 */}
            <div className="space-y-3">
              <label className="text-base font-semibold text-gray-700 flex items-center gap-3">
                <div className="w-6 h-6 bg-gradient-to-r from-indigo-400 to-blue-500 rounded-lg flex items-center justify-center">
                  <span className="text-white text-xs">📅</span>
                </div>
                选择日期
              </label>
              <div className="relative">
                <DatePicker
                  value={dateValue}
                  onChange={setDateValue}
                  placeholder="选择查询日期"
                  className="border-2 border-gray-200 focus:border-blue-400"
                />
              </div>
            </div>

            {/* ASIN输入 */}
            <div className="space-y-3">
              <label className="text-base font-semibold text-gray-700 flex items-center gap-3">
                <div className="w-6 h-6 bg-gradient-to-r from-blue-400 to-cyan-500 rounded-lg flex items-center justify-center">
                  <span className="text-white text-xs">🏷️</span>
                </div>
                ASIN产品编码
              </label>
              <div className="space-y-3">
                <div className="relative">
                  <textarea
                    placeholder="请输入ASIN，支持单个或多个（每行一个或用逗号分隔）&#10;&#10;示例：&#10;B0891XFDJ2&#10;B0891VDLV5&#10;B08V8T5JWT"
                    value={asinValue}
                    onChange={(e) => setAsinValue(e.target.value)}
                    className="w-full h-32 px-4 py-3 border-2 border-gray-200 rounded-xl focus:border-blue-400 focus:ring-4 focus:ring-blue-400/10 resize-none text-sm transition-all duration-200 placeholder:text-gray-400"
                  />
                  <div className="absolute top-3 right-3">
                    <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  </div>
                </div>
                <div className="flex items-center gap-2 text-sm text-gray-500">
                  <span className="w-4 h-4 bg-blue-100 rounded-full flex items-center justify-center">
                    <span className="text-blue-500 text-xs">ℹ️</span>
                  </span>
                  支持单个ASIN或多个ASIN（换行、逗号或空格分隔）
                </div>
              </div>
            </div>

            {/* 查询按钮 */}
            <div className="pt-4">
              <Button
                onClick={initial}
                disabled={loading}
                className="w-full h-14 bg-gradient-to-r from-indigo-500 via-blue-500 to-cyan-500 hover:from-indigo-600 hover:via-blue-600 hover:to-cyan-600 text-white font-semibold text-lg shadow-xl hover:shadow-2xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed rounded-xl"
              >
                {loading ? (
                  <>
                    <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-3"></div>
                    正在查询数据...
                  </>
                ) : (
                  <>
                    开始查询
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      {list.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-gradient-to-br from-indigo-50 to-blue-50 rounded-2xl p-6 border border-indigo-100 shadow-lg hover:shadow-xl transition-all duration-300">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-semibold text-indigo-600 mb-1">总记录数</p>
                <p className="text-3xl font-bold text-indigo-900">{list.length}</p>
                <p className="text-xs text-indigo-500 mt-1">条数据记录</p>
              </div>
              <div className="w-12 h-12 bg-gradient-to-br from-indigo-400 to-blue-500 rounded-xl flex items-center justify-center shadow-lg">
                <span className="text-white text-xl">📊</span>
              </div>
            </div>
          </div>

          <div className="bg-gradient-to-br from-emerald-50 to-green-50 rounded-2xl p-6 border border-emerald-100 shadow-lg hover:shadow-xl transition-all duration-300">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-semibold text-emerald-600 mb-1">平均价格</p>
                <p className="text-3xl font-bold text-emerald-900">
                  ${(() => {
                    const validPrices = list.filter((v): v is AsinData & { buyboxPrice: number } =>
                      typeof v.buyboxPrice === 'number'
                    );
                    return validPrices.length > 0
                      ? (validPrices.reduce((sum, v) => sum + v.buyboxPrice, 0) / validPrices.length).toFixed(2)
                      : '0.00';
                  })()}
                </p>
                <p className="text-xs text-emerald-500 mt-1">平均售价</p>
              </div>
              <div className="w-12 h-12 bg-gradient-to-br from-emerald-400 to-green-500 rounded-xl flex items-center justify-center shadow-lg">
                <span className="text-white text-xl">💰</span>
              </div>
            </div>
          </div>

          <div className="bg-gradient-to-br from-rose-50 to-pink-50 rounded-2xl p-6 border border-rose-100 shadow-lg hover:shadow-xl transition-all duration-300">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-semibold text-rose-600 mb-1">最高价格</p>
                <p className="text-3xl font-bold text-rose-900">
                  ${(() => {
                    const validPrices = list.filter((v): v is AsinData & { buyboxPrice: number } =>
                      typeof v.buyboxPrice === 'number'
                    );
                    return validPrices.length > 0
                      ? Math.max(...validPrices.map(v => v.buyboxPrice)).toFixed(2)
                      : '0.00';
                  })()}
                </p>
                <p className="text-xs text-rose-500 mt-1">峰值价格</p>
              </div>
              <div className="w-12 h-12 bg-gradient-to-br from-rose-400 to-pink-500 rounded-xl flex items-center justify-center shadow-lg">
                <span className="text-white text-xl">📈</span>
              </div>
            </div>
          </div>

          <div className="bg-gradient-to-br from-amber-50 to-orange-50 rounded-2xl p-6 border border-amber-100 shadow-lg hover:shadow-xl transition-all duration-300">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-semibold text-amber-600 mb-1">最低价格</p>
                <p className="text-3xl font-bold text-amber-900">
                  ${(() => {
                    const validPrices = list.filter((v): v is AsinData & { buyboxPrice: number } =>
                      typeof v.buyboxPrice === 'number'
                    );
                    return validPrices.length > 0
                      ? Math.min(...validPrices.map(v => v.buyboxPrice)).toFixed(2)
                      : '0.00';
                  })()}
                </p>
                <p className="text-xs text-amber-500 mt-1">最低售价</p>
              </div>
              <div className="w-12 h-12 bg-gradient-to-br from-amber-400 to-orange-500 rounded-xl flex items-center justify-center shadow-lg">
                <span className="text-white text-xl">📉</span>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Results Table */}
      <div className="bg-white rounded-3xl border border-gray-100 shadow-xl overflow-hidden">
        <div className="bg-gradient-to-r from-indigo-500 via-blue-500 to-cyan-500 px-8 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-xl font-bold text-white flex items-center gap-3">
                <div className="w-8 h-8 bg-white/20 rounded-xl flex items-center justify-center backdrop-blur-sm">
                  <span className="w-3 h-3 bg-white rounded-full"></span>
                </div>
                查询结果
              </h3>
              <p className="text-blue-100 text-sm mt-1">
                共 {list.length} 条数据记录
              </p>
            </div>
            {list.length > 0 && (
              <div className="flex items-center gap-3">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  <span className="px-4 py-2 bg-white/20 text-white rounded-xl text-sm font-medium backdrop-blur-sm">
                    数据已加载
                  </span>
                </div>
              </div>
            )}
          </div>
        </div>
        <div className="overflow-auto max-h-[600px]">
          <Table>
            <TableHeader>
              <TableRow className="bg-gradient-to-r from-gray-50 to-blue-50">
                <TableHead className="font-bold text-gray-700 py-4">
                  <div className="flex items-center gap-2">
                    日期
                  </div>
                </TableHead>
                <TableHead className="font-bold text-gray-700 py-4">
                  <div className="flex items-center gap-2">
                    {subBrsKeyOne || '排名1'}
                  </div>
                </TableHead>
                <TableHead className="font-bold text-gray-700 py-4">
                  <div className="flex items-center gap-2">
                    {subBrsKeyTwo || '排名2'}
                  </div>
                </TableHead>
                <TableHead className="font-bold text-gray-700 py-4">
                  <div className="flex items-center gap-2">
                    Buy Box价格
                  </div>
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {list.length > 0 ? (
                list.map((v: AsinData, index: number) => (
                  <TableRow key={index} className="hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 transition-all duration-200 border-b border-gray-100">
                    <TableCell className="font-semibold text-gray-800 py-4">{v.dates}</TableCell>
                    <TableCell className="py-4">
                      {v.subBsr?.[subBrsKeyOne] ? (
                        <span className="px-3 py-2 bg-gradient-to-r from-indigo-100 to-blue-100 text-indigo-700 rounded-xl text-sm font-bold border border-indigo-200">
                          #{v.subBsr[subBrsKeyOne].toLocaleString()}
                        </span>
                      ) : (
                        <span className="text-gray-400 font-medium">-</span>
                      )}
                    </TableCell>
                    <TableCell className="py-4">
                      {v.subBsr?.[subBrsKeyTwo] ? (
                        <span className="px-3 py-2 bg-gradient-to-r from-blue-100 to-cyan-100 text-blue-700 rounded-xl text-sm font-bold border border-blue-200">
                          #{v.subBsr[subBrsKeyTwo].toLocaleString()}
                        </span>
                      ) : (
                        <span className="text-gray-400 font-medium">-</span>
                      )}
                    </TableCell>
                    <TableCell className="py-4">
                      {v.buyboxPrice ? (
                        <span className="px-4 py-2 bg-gradient-to-r from-emerald-100 to-green-100 text-emerald-700 rounded-xl font-bold text-lg border border-emerald-200 shadow-sm">
                          ${v.buyboxPrice}
                        </span>
                      ) : (
                        <span className="text-gray-400 font-medium">-</span>
                      )}
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={4} className="text-center py-16">
                    <div className="flex flex-col items-center gap-4">
                      <div className="w-20 h-20 bg-gradient-to-br from-blue-100 to-indigo-100 rounded-2xl flex items-center justify-center">
                        <span className="text-3xl">🔍</span>
                      </div>
                      <div>
                        <p className="text-gray-600 font-semibold text-lg">暂无数据</p>
                        <p className="text-sm text-gray-500 mt-2">请输入ASIN并点击查询按钮开始分析</p>
                      </div>
                    </div>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </div>
    </div>
  );
}
