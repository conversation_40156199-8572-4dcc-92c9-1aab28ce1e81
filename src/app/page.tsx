"use client";
import React, { useEffect, useState } from "react";
import { CommandPopoverSelect } from "@/components/ui/command";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";

export default function Home() {
  const [selectData, setSelectData] = useState([]);
  const [dateValue, setDateValue] = useState<string>("");
  const [subBrsKeyOne, setSubBrsKeyOne] = useState("");
  const [subBrsKeyTwo, setSubBrsKeyTwo] = useState("");
  const [list, setList] = useState([]);
  const [asinValue, setAsinValue] = useState();

  useEffect(() => {
    initial();
  }, []);

  const initial = async () => {
    try {
      const response = await fetch("/api/list", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          asin: asinValue,
          date: dateValue
        }),
      });
      const { data } = await response.json();
      const getOneData = data[0];
      const { subBsr } = getOneData;
      const keyOne = Object.keys(subBsr)?.[0];
      const keyTwo = Object.keys(subBsr)?.[1];
      setSubBrsKeyOne(keyOne);
      setSubBrsKeyTwo(keyTwo);
      setSelectData(
        data?.map((v: any) => ({
          label: v.dates,
          value: v.dates,
        }))
      );
      setList(data);
    } catch (error) {
      console.log(error);
    }
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">ASIN数据查询</h2>
          <p className="text-muted-foreground">
            查询和分析Amazon产品的历史数据和趋势
          </p>
        </div>
      </div>

      {/* Search Controls */}
      <div className="flex flex-col gap-4 p-6 bg-card rounded-lg border">
        <h3 className="text-lg font-semibold">查询条件</h3>
        <div className="flex flex-col gap-4 md:flex-row">
          <div className="flex-1">
            <label className="text-sm font-medium mb-2 block">选择日期</label>
            <CommandPopoverSelect
              options={selectData}
              value={dateValue}
              setValue={setDateValue}
            />
          </div>
          <div className="flex-1">
            <label className="text-sm font-medium mb-2 block">ASIN</label>
            <Input
              type="text"
              placeholder="请输入ASIN"
              value={asinValue}
              onChange={(e) => setAsinValue(e.target.value as any)}
            />
          </div>
          <div className="flex items-end">
            <Button onClick={initial} className="w-full md:w-auto">
              查询数据
            </Button>
          </div>
        </div>
      </div>

      {/* Results Table */}
      <div className="bg-card rounded-lg border">
        <div className="p-6 border-b">
          <h3 className="text-lg font-semibold">查询结果</h3>
          <p className="text-sm text-muted-foreground mt-1">
            共 {list.length} 条记录
          </p>
        </div>
        <div className="overflow-auto max-h-[500px]">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>日期</TableHead>
                <TableHead>{subBrsKeyOne || '排名1'}</TableHead>
                <TableHead>{subBrsKeyTwo || '排名2'}</TableHead>
                <TableHead>Buy Box价格</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {list.length > 0 ? (
                list.map((v: any, index) => (
                  <TableRow key={index}>
                    <TableCell className="font-medium">{v.dates}</TableCell>
                    <TableCell>{v?.subBsr?.[subBrsKeyOne] || '-'}</TableCell>
                    <TableCell>{v?.subBsr?.[subBrsKeyTwo] || '-'}</TableCell>
                    <TableCell>{v.buyboxPrice ? `$${v.buyboxPrice}` : '-'}</TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={4} className="text-center py-8 text-muted-foreground">
                    暂无数据，请输入ASIN并点击查询
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </div>
    </div>
  );
}
