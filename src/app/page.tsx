"use client";
import React, { useEffect, useState } from "react";
import { CommandPopoverSelect } from "@/components/ui/command";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";

export default function Home() {
  const [selectData, setSelectData] = useState([]);
  const [dateValue, setDateValue] = useState<string>("");
  const [subBrsKeyOne, setSubBrsKeyOne] = useState("");
  const [subBrsKeyTwo, setSubBrsKeyTwo] = useState("");
  const [list, setList] = useState([]);
  const [asinValue, setAsinValue] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    initial();
  }, []);

  // 处理ASIN输入，支持单个和多个
  const processAsinInput = (input: string) => {
    if (!input.trim()) return [];

    // 分割ASIN，支持换行、逗号、空格等分隔符
    const asins = input
      .split(/[\n,\s]+/)
      .map(asin => asin.trim())
      .filter(asin => asin.length > 0);

    return asins;
  };

  const initial = async () => {
    setError(null);

    if (!asinValue.trim()) {
      setError('请输入ASIN');
      return;
    }

    const asins = processAsinInput(asinValue);
    if (asins.length === 0) {
      setError('请输入有效的ASIN');
      return;
    }

    setLoading(true);
    try {
      // 暂时只处理第一个ASIN，后续可以扩展为批量处理
      const firstAsin = asins[0];

      if (asins.length > 1) {
        setError(`当前版本暂时只支持单个ASIN查询，正在查询: ${firstAsin}`);
      }

      const response = await fetch("/api/list", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          asin: firstAsin,
          date: dateValue
        }),
      });

      if (!response.ok) {
        throw new Error(`请求失败: ${response.status}`);
      }

      const { data } = await response.json();

      if (data && data.length > 0) {
        const getOneData = data[0];
        const { subBsr } = getOneData;
        const keyOne = Object.keys(subBsr)?.[0];
        const keyTwo = Object.keys(subBsr)?.[1];
        setSubBrsKeyOne(keyOne);
        setSubBrsKeyTwo(keyTwo);
        setSelectData(
          data?.map((v: any) => ({
            label: v.dates,
            value: v.dates,
          }))
        );
        setList(data);
      } else {
        setList([]);
        setError('未找到数据，请检查ASIN是否正确');
      }
    } catch (error) {
      console.log(error);
      setError('查询失败，请检查网络连接');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-8">
      {/* Page Header */}
      <div className="text-center md:text-left">
        <div className="inline-flex items-center gap-2 px-3 py-1 bg-gray-900 text-white rounded-full text-sm font-medium mb-4">
          <span className="w-2 h-2 bg-white rounded-full"></span>
          Amazon数据分析平台
        </div>
        <h1 className="text-3xl md:text-4xl font-bold tracking-tight text-gray-900 mb-4">
          ASIN数据查询
        </h1>
        <p className="text-lg text-gray-600 max-w-2xl">
          查询和分析Amazon产品的历史数据和趋势，获取深度洞察
        </p>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 flex items-center gap-3">
          <div className="w-5 h-5 bg-red-500 rounded-full flex items-center justify-center flex-shrink-0">
            <span className="text-white text-xs">!</span>
          </div>
          <div>
            <p className="text-red-800 font-medium">错误</p>
            <p className="text-red-600 text-sm">{error}</p>
          </div>
        </div>
      )}

      {/* Search Controls */}
      <div className="bg-white rounded-2xl border border-gray-200 shadow-lg overflow-hidden">
        <div className="bg-gray-900 px-6 py-4">
          <h3 className="text-lg font-semibold text-white flex items-center gap-2">
            <div className="w-5 h-5 bg-white/20 rounded-full flex items-center justify-center">
              <span className="w-2 h-2 bg-white rounded-full"></span>
            </div>
            查询条件
          </h3>
        </div>
        <div className="p-6">
          <div className="space-y-6">
            {/* 日期选择 */}
            <div className="space-y-2">
              <label className="text-sm font-semibold text-gray-700 flex items-center gap-2">
                <span className="w-1 h-4 bg-gray-900 rounded-full"></span>
                选择日期
              </label>
              <CommandPopoverSelect
                options={selectData}
                value={dateValue}
                setValue={setDateValue}
              />
            </div>

            {/* ASIN输入 */}
            <div className="space-y-2">
              <label className="text-sm font-semibold text-gray-700 flex items-center gap-2">
                <span className="w-1 h-4 bg-gray-900 rounded-full"></span>
                ASIN
              </label>
              <div className="space-y-2">
                <textarea
                  placeholder="请输入ASIN，支持单个或多个（每行一个或用逗号分隔）&#10;例如：&#10;B0891XFDJ2&#10;B0891VDLV5&#10;B08V8T5JWT"
                  value={asinValue}
                  onChange={(e) => setAsinValue(e.target.value)}
                  className="w-full h-24 px-3 py-2 border border-gray-200 rounded-lg focus:border-gray-900 focus:ring-2 focus:ring-gray-900/10 resize-none text-sm"
                />
                <p className="text-xs text-gray-500">
                  支持单个ASIN或多个ASIN（换行、逗号或空格分隔）
                </p>
              </div>
            </div>

            {/* 查询按钮 */}
            <Button
              onClick={initial}
              disabled={loading}
              className="w-full h-12 bg-gray-900 hover:bg-gray-800 text-white font-medium shadow-lg hover:shadow-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                  查询中...
                </>
              ) : (
                <>
                  <span className="mr-2">🔍</span>
                  查询数据
                </>
              )}
            </Button>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      {list.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-white rounded-xl p-4 border border-gray-200 shadow-sm">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">总记录数</p>
                <p className="text-2xl font-bold text-gray-900">{list.length}</p>
              </div>
              <div className="w-10 h-10 bg-gray-900 rounded-lg flex items-center justify-center">
                <span className="text-white text-lg">📊</span>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl p-4 border border-gray-200 shadow-sm">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">平均价格</p>
                <p className="text-2xl font-bold text-gray-900">
                  ${list.filter(v => v.buyboxPrice).length > 0
                    ? (list.filter(v => v.buyboxPrice).reduce((sum, v) => sum + v.buyboxPrice, 0) / list.filter(v => v.buyboxPrice).length).toFixed(2)
                    : '0.00'}
                </p>
              </div>
              <div className="w-10 h-10 bg-gray-900 rounded-lg flex items-center justify-center">
                <span className="text-white text-lg">💰</span>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl p-4 border border-gray-200 shadow-sm">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">最高价格</p>
                <p className="text-2xl font-bold text-gray-900">
                  ${list.filter(v => v.buyboxPrice).length > 0
                    ? Math.max(...list.filter(v => v.buyboxPrice).map(v => v.buyboxPrice)).toFixed(2)
                    : '0.00'}
                </p>
              </div>
              <div className="w-10 h-10 bg-gray-900 rounded-lg flex items-center justify-center">
                <span className="text-white text-lg">📈</span>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl p-4 border border-gray-200 shadow-sm">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">最低价格</p>
                <p className="text-2xl font-bold text-gray-900">
                  ${list.filter(v => v.buyboxPrice).length > 0
                    ? Math.min(...list.filter(v => v.buyboxPrice).map(v => v.buyboxPrice)).toFixed(2)
                    : '0.00'}
                </p>
              </div>
              <div className="w-10 h-10 bg-gray-900 rounded-lg flex items-center justify-center">
                <span className="text-white text-lg">📉</span>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Results Table */}
      <div className="bg-white rounded-2xl border border-gray-200 shadow-lg overflow-hidden">
        <div className="bg-gray-900 px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-white flex items-center gap-2">
                <span className="w-2 h-2 bg-white rounded-full"></span>
                查询结果
              </h3>
              <p className="text-sm text-gray-300 mt-1">
                共 {list.length} 条记录
              </p>
            </div>
            {list.length > 0 && (
              <div className="flex items-center gap-2">
                <span className="px-3 py-1 bg-white/20 text-white rounded-full text-sm font-medium">
                  数据已加载
                </span>
              </div>
            )}
          </div>
        </div>
        <div className="overflow-auto max-h-[600px]">
          <Table>
            <TableHeader>
              <TableRow className="bg-gray-50/50">
                <TableHead className="font-semibold text-gray-700">📅 日期</TableHead>
                <TableHead className="font-semibold text-gray-700">🏷️ {subBrsKeyOne || '排名1'}</TableHead>
                <TableHead className="font-semibold text-gray-700">🏷️ {subBrsKeyTwo || '排名2'}</TableHead>
                <TableHead className="font-semibold text-gray-700">💰 Buy Box价格</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {list.length > 0 ? (
                list.map((v: any, index) => (
                  <TableRow key={index} className="hover:bg-gray-50/50 transition-colors">
                    <TableCell className="font-medium text-gray-900">{v.dates}</TableCell>
                    <TableCell className="text-gray-700">
                      {v?.subBsr?.[subBrsKeyOne] ? (
                        <span className="px-2 py-1 bg-gray-100 text-gray-800 rounded-md text-sm font-medium">
                          #{v.subBsr[subBrsKeyOne]}
                        </span>
                      ) : (
                        <span className="text-gray-400">-</span>
                      )}
                    </TableCell>
                    <TableCell className="text-gray-700">
                      {v?.subBsr?.[subBrsKeyTwo] ? (
                        <span className="px-2 py-1 bg-gray-100 text-gray-800 rounded-md text-sm font-medium">
                          #{v.subBsr[subBrsKeyTwo]}
                        </span>
                      ) : (
                        <span className="text-gray-400">-</span>
                      )}
                    </TableCell>
                    <TableCell>
                      {v.buyboxPrice ? (
                        <span className="px-3 py-1 bg-gray-900 text-white rounded-md font-semibold">
                          ${v.buyboxPrice}
                        </span>
                      ) : (
                        <span className="text-gray-400">-</span>
                      )}
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={4} className="text-center py-12">
                    <div className="flex flex-col items-center gap-3">
                      <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center">
                        <span className="text-2xl">🔍</span>
                      </div>
                      <div>
                        <p className="text-gray-500 font-medium">暂无数据</p>
                        <p className="text-sm text-gray-400 mt-1">请输入ASIN并点击查询按钮</p>
                      </div>
                    </div>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </div>
    </div>
  );
}
