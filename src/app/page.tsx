"use client";
import React, { useEffect, useState } from "react";
import { CommandPopoverSelect } from "@/components/ui/command";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";

export default function Home() {
  const [selectData, setSelectData] = useState([]);
  const [dateValue, setDateValue] = useState<string>("");
  const [subBrsKeyOne, setSubBrsKeyOne] = useState("");
  const [subBrsKeyTwo, setSubBrsKeyTwo] = useState("");
  const [list, setList] = useState([]);
  const [asinValue, setAsinValue] = useState();
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    initial();
  }, []);

  const initial = async () => {
    if (!asinValue) {
      alert('请输入ASIN');
      return;
    }

    setLoading(true);
    try {
      const response = await fetch("/api/list", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          asin: asinValue,
          date: dateValue
        }),
      });
      const { data } = await response.json();

      if (data && data.length > 0) {
        const getOneData = data[0];
        const { subBsr } = getOneData;
        const keyOne = Object.keys(subBsr)?.[0];
        const keyTwo = Object.keys(subBsr)?.[1];
        setSubBrsKeyOne(keyOne);
        setSubBrsKeyTwo(keyTwo);
        setSelectData(
          data?.map((v: any) => ({
            label: v.dates,
            value: v.dates,
          }))
        );
        setList(data);
      } else {
        setList([]);
        alert('未找到数据，请检查ASIN是否正确');
      }
    } catch (error) {
      console.log(error);
      alert('查询失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-8">
      {/* Page Header */}
      <div className="text-center md:text-left">
        <div className="inline-flex items-center gap-2 px-3 py-1 bg-blue-50 text-blue-700 rounded-full text-sm font-medium mb-4">
          <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
          Amazon数据分析平台
        </div>
        <h1 className="text-3xl md:text-4xl font-bold tracking-tight bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent mb-4">
          ASIN数据查询
        </h1>
        <p className="text-lg text-muted-foreground max-w-2xl">
          查询和分析Amazon产品的历史数据和趋势，获取深度洞察
        </p>
      </div>

      {/* Search Controls */}
      <div className="bg-gradient-to-br from-white to-gray-50 rounded-2xl border border-gray-200 shadow-sm overflow-hidden">
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 px-6 py-4">
          <h3 className="text-lg font-semibold text-white flex items-center gap-2">
            <div className="w-5 h-5 bg-white/20 rounded-full flex items-center justify-center">
              <span className="w-2 h-2 bg-white rounded-full"></span>
            </div>
            查询条件
          </h3>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="space-y-2">
              <label className="text-sm font-semibold text-gray-700 flex items-center gap-2">
                <span className="w-1 h-4 bg-blue-500 rounded-full"></span>
                选择日期
              </label>
              <div className="relative">
                <CommandPopoverSelect
                  options={selectData}
                  value={dateValue}
                  setValue={setDateValue}
                />
              </div>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-semibold text-gray-700 flex items-center gap-2">
                <span className="w-1 h-4 bg-purple-500 rounded-full"></span>
                ASIN
              </label>
              <Input
                type="text"
                placeholder="请输入ASIN (如: B0891XFDJ2)"
                value={asinValue}
                onChange={(e) => setAsinValue(e.target.value as any)}
                className="h-11 border-gray-200 focus:border-blue-500 focus:ring-blue-500/20"
              />
            </div>
            <div className="flex items-end">
              <Button
                onClick={initial}
                disabled={loading}
                className="w-full h-11 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium shadow-lg hover:shadow-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                    查询中...
                  </>
                ) : (
                  <>
                    <span className="mr-2">🔍</span>
                    查询数据
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      {list.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-4 border border-blue-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-blue-700">总记录数</p>
                <p className="text-2xl font-bold text-blue-900">{list.length}</p>
              </div>
              <div className="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center">
                <span className="text-white text-lg">📊</span>
              </div>
            </div>
          </div>

          <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-xl p-4 border border-green-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-green-700">平均价格</p>
                <p className="text-2xl font-bold text-green-900">
                  ${list.filter(v => v.buyboxPrice).length > 0
                    ? (list.filter(v => v.buyboxPrice).reduce((sum, v) => sum + v.buyboxPrice, 0) / list.filter(v => v.buyboxPrice).length).toFixed(2)
                    : '0.00'}
                </p>
              </div>
              <div className="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center">
                <span className="text-white text-lg">💰</span>
              </div>
            </div>
          </div>

          <div className="bg-gradient-to-br from-purple-50 to-purple-100 rounded-xl p-4 border border-purple-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-purple-700">最高价格</p>
                <p className="text-2xl font-bold text-purple-900">
                  ${list.filter(v => v.buyboxPrice).length > 0
                    ? Math.max(...list.filter(v => v.buyboxPrice).map(v => v.buyboxPrice)).toFixed(2)
                    : '0.00'}
                </p>
              </div>
              <div className="w-10 h-10 bg-purple-500 rounded-lg flex items-center justify-center">
                <span className="text-white text-lg">📈</span>
              </div>
            </div>
          </div>

          <div className="bg-gradient-to-br from-orange-50 to-orange-100 rounded-xl p-4 border border-orange-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-orange-700">最低价格</p>
                <p className="text-2xl font-bold text-orange-900">
                  ${list.filter(v => v.buyboxPrice).length > 0
                    ? Math.min(...list.filter(v => v.buyboxPrice).map(v => v.buyboxPrice)).toFixed(2)
                    : '0.00'}
                </p>
              </div>
              <div className="w-10 h-10 bg-orange-500 rounded-lg flex items-center justify-center">
                <span className="text-white text-lg">📉</span>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Results Table */}
      <div className="bg-white rounded-2xl border border-gray-200 shadow-sm overflow-hidden">
        <div className="bg-gradient-to-r from-gray-50 to-gray-100 px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                查询结果
              </h3>
              <p className="text-sm text-gray-600 mt-1">
                共 {list.length} 条记录
              </p>
            </div>
            {list.length > 0 && (
              <div className="flex items-center gap-2">
                <span className="px-3 py-1 bg-green-100 text-green-700 rounded-full text-sm font-medium">
                  数据已加载
                </span>
              </div>
            )}
          </div>
        </div>
        <div className="overflow-auto max-h-[600px]">
          <Table>
            <TableHeader>
              <TableRow className="bg-gray-50/50">
                <TableHead className="font-semibold text-gray-700">📅 日期</TableHead>
                <TableHead className="font-semibold text-gray-700">🏷️ {subBrsKeyOne || '排名1'}</TableHead>
                <TableHead className="font-semibold text-gray-700">🏷️ {subBrsKeyTwo || '排名2'}</TableHead>
                <TableHead className="font-semibold text-gray-700">💰 Buy Box价格</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {list.length > 0 ? (
                list.map((v: any, index) => (
                  <TableRow key={index} className="hover:bg-gray-50/50 transition-colors">
                    <TableCell className="font-medium text-gray-900">{v.dates}</TableCell>
                    <TableCell className="text-gray-700">
                      {v?.subBsr?.[subBrsKeyOne] ? (
                        <span className="px-2 py-1 bg-blue-100 text-blue-700 rounded-md text-sm font-medium">
                          #{v.subBsr[subBrsKeyOne]}
                        </span>
                      ) : (
                        <span className="text-gray-400">-</span>
                      )}
                    </TableCell>
                    <TableCell className="text-gray-700">
                      {v?.subBsr?.[subBrsKeyTwo] ? (
                        <span className="px-2 py-1 bg-purple-100 text-purple-700 rounded-md text-sm font-medium">
                          #{v.subBsr[subBrsKeyTwo]}
                        </span>
                      ) : (
                        <span className="text-gray-400">-</span>
                      )}
                    </TableCell>
                    <TableCell>
                      {v.buyboxPrice ? (
                        <span className="px-3 py-1 bg-green-100 text-green-700 rounded-md font-semibold">
                          ${v.buyboxPrice}
                        </span>
                      ) : (
                        <span className="text-gray-400">-</span>
                      )}
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={4} className="text-center py-12">
                    <div className="flex flex-col items-center gap-3">
                      <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center">
                        <span className="text-2xl">🔍</span>
                      </div>
                      <div>
                        <p className="text-gray-500 font-medium">暂无数据</p>
                        <p className="text-sm text-gray-400 mt-1">请输入ASIN并点击查询按钮</p>
                      </div>
                    </div>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </div>
    </div>
  );
}
