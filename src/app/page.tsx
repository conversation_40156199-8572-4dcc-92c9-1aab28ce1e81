"use client";
import React, { useEffect, useState } from "react";
import { CommandPopoverSelect } from "@/components/ui/command";
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableFooter,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";

export default function Home() {
  const [selectData, setSelectData] = useState([]);
  const [dateValue, setDateValue] = useState<string>("");
  const [subBrsKeyOne, setSubBrsKeyOne] = useState("");
  const [subBrsKeyTwo, setSubBrsKeyTwo] = useState("");
  const [list, setList] = useState([]);
  const [asinValue, setAsinValue] = useState();

  useEffect(() => {
    initial();
  }, []);

  const initial = async () => {
    try {
      const response = await fetch("/api/list", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          asin: asinValue,
          date: dateValue
        }),
      });
      const { data } = await response.json();
      const getOneData = data[0];
      const { subBsr } = getOneData;
      const keyOne = Object.keys(subBsr)?.[0];
      const keyTwo = Object.keys(subBsr)?.[1];
      setSubBrsKeyOne(keyOne);
      setSubBrsKeyTwo(keyTwo);
      setSelectData(
        data?.map((v: any) => ({
          label: v.dates,
          value: v.dates,
        }))
      );
      setList(data);
    } catch (error) {
      console.log(error);
    }
  };

  return (
    <div className="grid grid-rows-[20px_1fr_20px] items-center justify-items-center min-h-screen p-8 pb-20 gap-16 sm:p-20 font-[family-name:var(--font-geist-sans)]">
      <div className="flex flex-row gap-[10px]">
        <CommandPopoverSelect
          options={selectData}
          value={dateValue}
          setValue={setDateValue}
        />
        <Input type="text" placeholder="asin" value={asinValue} onChange={(e) =>{
          setAsinValue(e.target.value as any)
        }}  />
        <Button onClick={initial}>查询</Button>
      </div>
      <div className="h-[400px] overflow-auto border rounded-md w-full">
        <Table>
          <TableCaption>A list of your recent invoices.</TableCaption>
          <TableHeader>
            <TableRow>
              <TableHead>dates</TableHead>
              <TableHead>{subBrsKeyOne}</TableHead>
              <TableHead>{subBrsKeyTwo}</TableHead>
              <TableHead>buyboxPrice</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {list.map((v: any, index) => (
              <TableRow key={index}>
                <TableCell className="font-medium">{v.dates}</TableCell>
                <TableCell>{v?.subBsr?.[subBrsKeyOne]}</TableCell>
                <TableCell>{v?.subBsr?.[subBrsKeyTwo]}</TableCell>
                <TableCell>{v.buyboxPrice}</TableCell>
              </TableRow>
            ))}
          </TableBody>
          <TableFooter>
            {/* <TableRow>
          <TableCell colSpan={3}>Total</TableCell>
          <TableCell className="text-right">$2,500.00</TableCell>
        </TableRow> */}
          </TableFooter>
        </Table>
      </div>
    </div>
  );
}
