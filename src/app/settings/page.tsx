'use client';

import React, { useState } from 'react';
import { Settings, Database, Bell, Shield, Palette } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

export default function SettingsPage() {
  const [settings, setSettings] = useState({
    apiTimeout: '20',
    maxConcurrent: '8',
    cacheTime: '5',
    notifications: true,
    autoExport: false,
    theme: 'system'
  });

  const handleSave = () => {
    // 这里可以添加保存设置的逻辑
    console.log('Settings saved:', settings);
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div>
        <h2 className="text-2xl font-bold tracking-tight">系统设置</h2>
        <p className="text-muted-foreground">
          配置系统参数和个人偏好设置
        </p>
      </div>

      {/* Settings Sections */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* API Settings */}
        <div className="bg-card border rounded-lg p-6">
          <div className="flex items-center gap-3 mb-4">
            <Database className="h-5 w-5 text-blue-500" />
            <h3 className="text-lg font-semibold">API设置</h3>
          </div>
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium mb-2 block">
                请求超时时间 (秒)
              </label>
              <Input
                type="number"
                value={settings.apiTimeout}
                onChange={(e) => setSettings({...settings, apiTimeout: e.target.value})}
                placeholder="20"
              />
              <p className="text-xs text-muted-foreground mt-1">
                API请求的最大等待时间
              </p>
            </div>
            
            <div>
              <label className="text-sm font-medium mb-2 block">
                最大并发请求数
              </label>
              <Input
                type="number"
                value={settings.maxConcurrent}
                onChange={(e) => setSettings({...settings, maxConcurrent: e.target.value})}
                placeholder="8"
              />
              <p className="text-xs text-muted-foreground mt-1">
                同时进行的最大请求数量
              </p>
            </div>
            
            <div>
              <label className="text-sm font-medium mb-2 block">
                缓存时间 (分钟)
              </label>
              <Input
                type="number"
                value={settings.cacheTime}
                onChange={(e) => setSettings({...settings, cacheTime: e.target.value})}
                placeholder="5"
              />
              <p className="text-xs text-muted-foreground mt-1">
                数据缓存的有效时间
              </p>
            </div>
          </div>
        </div>

        {/* Notification Settings */}
        <div className="bg-card border rounded-lg p-6">
          <div className="flex items-center gap-3 mb-4">
            <Bell className="h-5 w-5 text-orange-500" />
            <h3 className="text-lg font-semibold">通知设置</h3>
          </div>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">启用通知</p>
                <p className="text-sm text-muted-foreground">
                  接收系统通知和提醒
                </p>
              </div>
              <Button
                variant={settings.notifications ? "default" : "outline"}
                size="sm"
                onClick={() => setSettings({...settings, notifications: !settings.notifications})}
              >
                {settings.notifications ? "已启用" : "已禁用"}
              </Button>
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">自动导出</p>
                <p className="text-sm text-muted-foreground">
                  批量查询完成后自动导出Excel
                </p>
              </div>
              <Button
                variant={settings.autoExport ? "default" : "outline"}
                size="sm"
                onClick={() => setSettings({...settings, autoExport: !settings.autoExport})}
              >
                {settings.autoExport ? "已启用" : "已禁用"}
              </Button>
            </div>
          </div>
        </div>

        {/* Security Settings */}
        <div className="bg-card border rounded-lg p-6">
          <div className="flex items-center gap-3 mb-4">
            <Shield className="h-5 w-5 text-green-500" />
            <h3 className="text-lg font-semibold">安全设置</h3>
          </div>
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium mb-2 block">
                API密钥
              </label>
              <Input
                type="password"
                value="••••••••••••••••"
                readOnly
                className="bg-muted"
              />
              <p className="text-xs text-muted-foreground mt-1">
                用于访问SIF API的认证密钥
              </p>
            </div>
            
            <Button variant="outline" size="sm">
              更新API密钥
            </Button>
          </div>
        </div>

        {/* Appearance Settings */}
        <div className="bg-card border rounded-lg p-6">
          <div className="flex items-center gap-3 mb-4">
            <Palette className="h-5 w-5 text-purple-500" />
            <h3 className="text-lg font-semibold">外观设置</h3>
          </div>
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium mb-2 block">
                主题模式
              </label>
              <div className="grid grid-cols-3 gap-2">
                {['light', 'dark', 'system'].map((theme) => (
                  <Button
                    key={theme}
                    variant={settings.theme === theme ? "default" : "outline"}
                    size="sm"
                    onClick={() => setSettings({...settings, theme})}
                  >
                    {theme === 'light' ? '浅色' : theme === 'dark' ? '深色' : '跟随系统'}
                  </Button>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Save Button */}
      <div className="flex justify-end">
        <Button onClick={handleSave} className="px-8">
          <Settings className="h-4 w-4 mr-2" />
          保存设置
        </Button>
      </div>

      {/* System Info */}
      <div className="bg-card border rounded-lg p-6">
        <h3 className="text-lg font-semibold mb-4">系统信息</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <p className="text-muted-foreground">版本</p>
            <p className="font-medium">1.0.0</p>
          </div>
          <div>
            <p className="text-muted-foreground">最后更新</p>
            <p className="font-medium">2024-01-15</p>
          </div>
          <div>
            <p className="text-muted-foreground">运行环境</p>
            <p className="font-medium">Next.js 15.3.0</p>
          </div>
          <div>
            <p className="text-muted-foreground">数据库状态</p>
            <p className="font-medium text-green-600">正常</p>
          </div>
        </div>
      </div>
    </div>
  );
}
