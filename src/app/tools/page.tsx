'use client';

import React from 'react';
import Link from 'next/link';
import { FileSpreadsheet, Database, Download, Upload } from 'lucide-react';
import { Button } from '@/components/ui/button';

export default function ToolsPage() {
  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div>
        <h2 className="text-2xl font-bold tracking-tight">工具集</h2>
        <p className="text-muted-foreground">
          Excel处理、批量操作和数据导入导出工具
        </p>
      </div>

      {/* Tools Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* Excel Processing Tool */}
        <div className="bg-card border rounded-lg p-6 hover:shadow-md transition-shadow">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-2 bg-green-100 rounded-lg">
              <FileSpreadsheet className="h-6 w-6 text-green-600" />
            </div>
            <h3 className="text-lg font-semibold">Excel处理</h3>
          </div>
          <p className="text-muted-foreground mb-4">
            处理Excel文件，自动填充空白单元格，计算平均值
          </p>
          <div className="space-y-2">
            <Button className="w-full" variant="outline">
              <Upload className="h-4 w-4 mr-2" />
              上传Excel文件
            </Button>
            <p className="text-xs text-muted-foreground">
              支持 .xlsx, .xls 格式
            </p>
          </div>
        </div>

        {/* Batch Query Tool */}
        <div className="bg-card border rounded-lg p-6 hover:shadow-md transition-shadow">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Database className="h-6 w-6 text-blue-600" />
            </div>
            <h3 className="text-lg font-semibold">批量查询</h3>
          </div>
          <p className="text-muted-foreground mb-4">
            批量查询多个ASIN的历史数据，支持Excel导出
          </p>
          <div className="space-y-2">
            <Button className="w-full">
              <Database className="h-4 w-4 mr-2" />
              开始批量查询
            </Button>
            <p className="text-xs text-muted-foreground">
              最多支持15个ASIN同时查询
            </p>
          </div>
        </div>

        {/* Data Export Tool */}
        <div className="bg-card border rounded-lg p-6 hover:shadow-md transition-shadow">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-2 bg-purple-100 rounded-lg">
              <Download className="h-6 w-6 text-purple-600" />
            </div>
            <h3 className="text-lg font-semibold">数据导出</h3>
          </div>
          <p className="text-muted-foreground mb-4">
            导出查询结果为Excel格式，支持多工作表
          </p>
          <div className="space-y-2">
            <Button className="w-full" variant="outline">
              <Download className="h-4 w-4 mr-2" />
              导出数据
            </Button>
            <p className="text-xs text-muted-foreground">
              自动生成格式化报告
            </p>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-card border rounded-lg p-6">
        <h3 className="text-lg font-semibold mb-4">快速操作</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-3">
            <h4 className="font-medium">Excel工具</h4>
            <div className="space-y-2">
              <Button variant="outline" size="sm" className="w-full justify-start">
                处理销售数据Excel
              </Button>
              <Button variant="outline" size="sm" className="w-full justify-start">
                生成ASIN报告
              </Button>
              <Button variant="outline" size="sm" className="w-full justify-start">
                数据清洗工具
              </Button>
            </div>
          </div>
          
          <div className="space-y-3">
            <h4 className="font-medium">批量操作</h4>
            <div className="space-y-2">
              <Button variant="outline" size="sm" className="w-full justify-start">
                批量ASIN验证
              </Button>
              <Button variant="outline" size="sm" className="w-full justify-start">
                价格监控设置
              </Button>
              <Button variant="outline" size="sm" className="w-full justify-start">
                排名趋势分析
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Usage Statistics */}
      <div className="bg-card border rounded-lg p-6">
        <h3 className="text-lg font-semibold mb-4">使用统计</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center">
            <p className="text-2xl font-bold text-green-600">156</p>
            <p className="text-sm text-muted-foreground">Excel文件处理</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold text-blue-600">89</p>
            <p className="text-sm text-muted-foreground">批量查询任务</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold text-purple-600">234</p>
            <p className="text-sm text-muted-foreground">数据导出次数</p>
          </div>
        </div>
      </div>
    </div>
  );
}
