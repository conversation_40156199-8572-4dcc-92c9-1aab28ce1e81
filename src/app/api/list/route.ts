import dayjs from "dayjs";
import isSameOrAfter from "dayjs/plugin/isSameOrAfter";
dayjs.extend(isSameOrAfter);
import { NextRequest, NextResponse } from "next/server";
import { list } from "@/app/services/list";

export async function POST(request: NextRequest) {
  try {
    const body = await request?.json();
    const { country, asin, date } = body ?? {};
    const response = await list(country, asin);
    const { code, data } = response;
    if (code === 1) {
      const { dates, subBsr, buyboxPrice } = data;
      const monthlyData = new Map<
        string,
        {
          buyboxPrices: number[];
          subBsr: { [key: string]: (number | null)[] };
        }
      >();

      dates.forEach((dateStr: any, index: number) => {
        const monthKey = dayjs(dateStr).format("YYYY-MM-DD");

        if (!monthlyData.has(monthKey)) {
          monthlyData.set(monthKey, {
            buyboxPrices: [],
            subBsr: Object.keys(subBsr).reduce((acc, key) => {
              acc[key] = [];
              return acc;
            }, {} as { [key: string]: (number | null)[] }),
          });
        }

        const monthData = monthlyData.get(monthKey)!;
        monthData.buyboxPrices.push(buyboxPrice[index]);
        Object.entries(subBsr).forEach(([category, values]) => {
          const val = (values as any[])[index];
          monthData.subBsr[category].push(val !== undefined ? val : null);
        });
      }); // 这里结束forEach

      const handleDataResult = Array.from(monthlyData).map(([month, data]) => {
        const validPrices = data.buyboxPrices.filter(
          Number.isFinite
        ) as number[];
        const avgPrice =
          validPrices.length > 0
            ? Number(
                (
                  validPrices.reduce((a, b) => a + b, 0) / validPrices.length
                ).toFixed(2) // 添加toFixed保留两位小数
              )
            : null; // 确保avgPrice为null时不会报错

        const avgSubBsr = Object.entries(data.subBsr).reduce(
          (acc, [category, values]) => {
            const validValues = values.filter(
              (v) => typeof v === "number"
            ) as number[];
            acc[category] =
              validValues.length > 0
                ? Math.floor(
                    validValues.reduce((a, b) => a + b, 0) / validValues.length
                  )
                : null;
            return acc;
          },
          {} as { [key: string]: number | null }
        );

        return {
          dates: month,
          subBsr: avgSubBsr,
          buyboxPrice: avgPrice,
        };
      });
      if (date) {
        // 修改为isSameOrAfter确保包含当天数据
        const filterData = handleDataResult.filter((item) => {
          return dayjs(item.dates).isSameOrAfter(dayjs(date), "day");
        });
        return NextResponse.json({ data: filterData });
      } else {
        return NextResponse.json({ data: handleDataResult });
      }

    }
  } catch (error) {
    console.error(" API Error:", error);
    return NextResponse.json(
      { error: "Failed to process request" },
      { status: 500 }
    );
  }
}
