import { NextRequest, NextResponse } from 'next/server';
import { PerformanceMonitor } from '@/config/performanceConfig';

/**
 * GET /api/sjf/stats
 * 获取性能统计信息
 */
export async function GET(request: NextRequest) {
  try {
    const stats = PerformanceMonitor.getAllStats();
    return NextResponse.json({
      performanceStats: stats,
      timestamp: new Date().toISOString(),
      message: "Performance statistics for SJF API requests"
    });
  } catch (error: any) {
    console.error("Error getting performance stats:", error);
    return NextResponse.json(
      {
        error: "Failed to retrieve performance statistics",
        details: error.message
      },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/sjf/stats
 * 清除性能统计信息
 */
export async function DELETE(request: NextRequest) {
  try {
    PerformanceMonitor.clearStats();
    return NextResponse.json({
      message: "Performance statistics cleared successfully",
      timestamp: new Date().toISOString()
    });
  } catch (error: any) {
    console.error("Error clearing performance stats:", error);
    return NextResponse.json(
      {
        error: "Failed to clear performance statistics",
        details: error.message
      },
      { status: 500 }
    );
  }
}
