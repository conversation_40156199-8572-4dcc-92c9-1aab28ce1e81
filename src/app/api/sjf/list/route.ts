import { NextRequest, NextResponse } from 'next/server';
import dayjs from 'dayjs';
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter';
import SjfService from '@/services/sjfService';
import ExcelProcessingService from '@/services/excelProcessingService';

dayjs.extend(isSameOrAfter);

// 定义处理后的数据类型
interface ProcessedAsinData {
  dates: string;
  buyboxPrice: number | null;
  [key: string]: any; // 用于动态的 subBsr 字段
}

// 定义批量响应的数据类型
interface BatchAsinResult {
  asin: string;
  data: ProcessedAsinData[];
}

// 性能监控辅助函数
function logPerformance(operation: string, startTime: number, itemCount?: number) {
  const duration = Date.now() - startTime;
  const itemInfo = itemCount ? ` (${itemCount} items)` : '';
  console.log(`[Performance] ${operation}${itemInfo}: ${duration}ms`);

  if (duration > 3000) {
    console.warn(`[Performance Warning] ${operation} took ${duration}ms, consider optimization`);
  }
}

/**
 * 处理单个 ASIN 的数据
 * @param apiData API 返回的原始数据
 * @param date 可选的日期过滤参数
 * @returns 处理后的数据数组
 */
function processAsinData(apiData: any, date?: string): ProcessedAsinData[] {
  const { dates, subBsr, buyboxPrice } = apiData;

  // 检查数据是否存在
  if (!dates || !Array.isArray(dates)) {
    console.warn('No dates data found in API response');
    return [];
  }

  const monthlyData = new Map<
    string,
    {
      buyboxPrices: number[];
      subBsr: { [key: string]: (number | null)[] };
    }
  >();

  // 按月份分组数据
  dates.forEach((dateStr: string, index: number) => {
    const monthKey = dayjs(dateStr).format('YYYY-MM');
    
    if (!monthlyData.has(monthKey)) {
      monthlyData.set(monthKey, {
        buyboxPrices: [],
        subBsr: {}
      });
    }

    const monthData = monthlyData.get(monthKey)!;
    
    // 收集 buyboxPrice
    if (buyboxPrice && buyboxPrice[index] !== null && buyboxPrice[index] !== undefined) {
      monthData.buyboxPrices.push(buyboxPrice[index]);
    }

    // 收集 subBsr 数据
    if (subBsr) {
      Object.keys(subBsr).forEach(key => {
        if (!monthData.subBsr[key]) {
          monthData.subBsr[key] = [];
        }
        if (subBsr[key][index] !== null && subBsr[key][index] !== undefined) {
          monthData.subBsr[key].push(subBsr[key][index]);
        }
      });
    }
  });

  // 计算每月平均值
  const handleDataResult: ProcessedAsinData[] = [];
  
  for (const [monthKey, data] of monthlyData) {
    const result: ProcessedAsinData = {
      dates: monthKey,
      buyboxPrice: data.buyboxPrices.length > 0 
        ? Math.round(data.buyboxPrices.reduce((sum, price) => sum + price, 0) / data.buyboxPrices.length)
        : null
    };

    // 计算 subBsr 平均值
    Object.keys(data.subBsr).forEach(key => {
      const values = data.subBsr[key];
      if (values.length > 0) {
        const validValues = values.filter((val): val is number => val !== null && val !== undefined);
        if (validValues.length > 0) {
          result[`subBsr_${key}`] = Math.round(validValues.reduce((sum, val) => sum + val, 0) / validValues.length);
        } else {
          result[`subBsr_${key}`] = null;
        }
      } else {
        result[`subBsr_${key}`] = null;
      }
    });

    handleDataResult.push(result);
  }

  // 按日期排序
  handleDataResult.sort((a, b) => {
    return dayjs(a.dates).valueOf() - dayjs(b.dates).valueOf();
  });

  // 应用日期过滤
  if (date) {
    return handleDataResult.filter((item) => {
      return dayjs(item.dates).isSameOrAfter(dayjs(date), "day");
    });
  }

  return handleDataResult;
}

/**
 * POST /api/sjf/list
 * 获取并处理 ASIN 列表数据
 */
export async function POST(request: NextRequest) {
  const requestStartTime = Date.now();

  try {
    const body = await request.json();
    const { country, asin, date, format } = body;

    // 参数校验
    if (!country || !asin) {
      return NextResponse.json(
        { error: "Missing required parameters: country and asin" },
        { status: 400 }
      );
    }

    // 检查 asin 是否为数组
    const isAsinArray = Array.isArray(asin);
    const asinList: string[] = isAsinArray ? asin : [asin];

    // 根据请求类型设置默认格式：批量请求默认Excel，单个请求默认JSON
    const defaultFormat = isAsinArray ? 'excel' : 'json';
    const responseFormat = format || defaultFormat;

    // 验证format参数
    if (responseFormat && !['json', 'excel'].includes(responseFormat)) {
      return NextResponse.json(
        { error: "Invalid format parameter. Must be 'json' or 'excel'" },
        { status: 400 }
      );
    }

    // 验证 asin 数组不为空
    if (asinList.length === 0) {
      return NextResponse.json(
        { error: "ASIN array cannot be empty" },
        { status: 400 }
      );
    }

    console.log(`Processing ${isAsinArray ? 'batch' : 'single'} request for ${asinList.length} ASIN(s) in ${country}, format: ${responseFormat}`);

    let processedResults: BatchAsinResult[];

    if (isAsinArray) {
      // 批量处理
      const batchStartTime = Date.now();
      const batchResults = await SjfService.getBatchAsinHistory(country, asinList);
      logPerformance('Batch API requests', batchStartTime, asinList.length);

      // 处理批量结果
      const processingStartTime = Date.now();
      processedResults = batchResults.map(result => {
        if (result.data && result.data.code === 1) {
          return {
            asin: result.asin,
            data: processAsinData(result.data.data, date)
          };
        } else {
          console.warn(`Failed to process ASIN ${result.asin}:`, result.error || 'API returned non-success code');
          return {
            asin: result.asin,
            data: []
          };
        }
      });
      logPerformance('Batch data processing', processingStartTime, asinList.length);
    } else {
      // 单个处理
      const singleStartTime = Date.now();
      const singleResult = await SjfService.getAsinHistory(country, asinList[0]);
      logPerformance('Single API request', singleStartTime);

      const processingStartTime = Date.now();
      if (singleResult && singleResult.code === 1) {
        processedResults = [{
          asin: asinList[0],
          data: processAsinData(singleResult.data, date)
        }];
      } else {
        console.warn(`Failed to process ASIN ${asinList[0]}:`, 'API returned non-success code');
        processedResults = [{
          asin: asinList[0],
          data: []
        }];
      }
      logPerformance('Single data processing', processingStartTime);
    }

    // 根据格式返回响应
    if (responseFormat === 'excel') {
      const excelStartTime = Date.now();
      const excelBuffer = await ExcelProcessingService.createAsinDataExcel(processedResults);
      logPerformance('Excel generation', excelStartTime, asinList.length);

      const filename = `asin-data-${country}-${dayjs().format('YYYY-MM-DD-HH-mm-ss')}.xlsx`;
      
      return new NextResponse(excelBuffer, {
        status: 200,
        headers: {
          'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          'Content-Disposition': `attachment; filename="${filename}"`,
          'Content-Length': excelBuffer.length.toString(),
        },
      });
    } else {
      // JSON 响应
      const responseData = isAsinArray ? processedResults : processedResults[0].data;
      
      // 记录总体性能
      logPerformance('Total request', requestStartTime, asinList.length);

      return NextResponse.json({
        success: true,
        data: responseData,
        metadata: {
          country,
          asinCount: asinList.length,
          format: responseFormat,
          processedAt: new Date().toISOString(),
          ...(date && { dateFilter: date })
        }
      });
    }

  } catch (error: any) {
    console.error("Error in /api/sjf/list:", error);

    // 区分是 SIF API 错误还是其他内部错误
    if (error.message?.includes("SIF API request failed")) {
      return NextResponse.json(
        {
          error: "Failed to fetch data from SIF API",
          details: error.message
        },
        { status: 502 }
      );
    }

    // 处理 Promise.all 中的部分失败情况
    if (error.name === 'AggregateError' || error.errors) {
      return NextResponse.json(
        {
          error: "Some ASIN requests failed",
          details: "Partial success - some ASINs could not be processed",
          partialResults: true
        },
        { status: 207 }
      );
    }

    return NextResponse.json(
      {
        error: "Failed to process request",
        details: error.message
      },
      { status: 500 }
    );
  }
}
