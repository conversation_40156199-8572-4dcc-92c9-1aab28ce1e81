import { NextRequest, NextResponse } from 'next/server';
import ExcelProcessingService from '@/services/excelProcessingService';

/**
 * POST /api/sales/process-sales-excel
 * 处理销售Excel文件
 */
export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('excelFile') as File;

    if (!file) {
      return NextResponse.json(
        { message: 'No file uploaded.' },
        { status: 400 }
      );
    }

    // 将文件转换为Buffer
    const arrayBuffer = await file.arrayBuffer();
    const fileBuffer = Buffer.from(arrayBuffer);
    const originalFilename = file.name;

    // 处理Excel文件
    const processedBuffer = await ExcelProcessingService.processSheetAverages(fileBuffer);

    // 生成新的文件名
    const timestamp = new Date().toISOString().replace(/[-:.]/g, '').slice(0, 14); // YYYYMMDDHHmmss
    const lastDotIndex = originalFilename.lastIndexOf('.');
    const baseFilename = lastDotIndex > -1 
      ? originalFilename.substring(0, lastDotIndex) 
      : originalFilename;
    const extension = lastDotIndex > -1 
      ? originalFilename.substring(lastDotIndex) 
      : '.xlsx';
    const newFilename = `${baseFilename}_processed_${timestamp}${extension}`;

    return new NextResponse(processedBuffer, {
      status: 200,
      headers: {
        'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'Content-Disposition': `attachment; filename="${newFilename}"`,
        'Content-Length': processedBuffer.length.toString(),
      },
    });

  } catch (error: any) {
    console.error('Error processing Excel file:', error);
    return NextResponse.json(
      {
        error: 'Failed to process Excel file',
        details: error.message
      },
      { status: 500 }
    );
  }
}
