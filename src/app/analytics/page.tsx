'use client';

import React from 'react';
import { BarChart3, TrendingUp, PieChart, Activity } from 'lucide-react';

export default function AnalyticsPage() {
  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div>
        <h2 className="text-2xl font-bold tracking-tight">数据分析</h2>
        <p className="text-muted-foreground">
          深度分析ASIN数据，发现趋势和洞察
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-card border rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">总查询次数</p>
              <p className="text-2xl font-bold">1,234</p>
            </div>
            <Activity className="h-8 w-8 text-blue-500" />
          </div>
          <p className="text-xs text-muted-foreground mt-2">
            +12% 比上月
          </p>
        </div>

        <div className="bg-card border rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">活跃ASIN</p>
              <p className="text-2xl font-bold">567</p>
            </div>
            <BarChart3 className="h-8 w-8 text-green-500" />
          </div>
          <p className="text-xs text-muted-foreground mt-2">
            +8% 比上月
          </p>
        </div>

        <div className="bg-card border rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">平均价格</p>
              <p className="text-2xl font-bold">$89.50</p>
            </div>
            <TrendingUp className="h-8 w-8 text-orange-500" />
          </div>
          <p className="text-xs text-muted-foreground mt-2">
            -2% 比上月
          </p>
        </div>

        <div className="bg-card border rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">数据准确率</p>
              <p className="text-2xl font-bold">98.5%</p>
            </div>
            <PieChart className="h-8 w-8 text-purple-500" />
          </div>
          <p className="text-xs text-muted-foreground mt-2">
            +0.5% 比上月
          </p>
        </div>
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-card border rounded-lg p-6">
          <h3 className="text-lg font-semibold mb-4">价格趋势</h3>
          <div className="h-64 flex items-center justify-center bg-muted rounded-lg">
            <p className="text-muted-foreground">图表组件将在这里显示</p>
          </div>
        </div>

        <div className="bg-card border rounded-lg p-6">
          <h3 className="text-lg font-semibold mb-4">排名分布</h3>
          <div className="h-64 flex items-center justify-center bg-muted rounded-lg">
            <p className="text-muted-foreground">图表组件将在这里显示</p>
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="bg-card border rounded-lg">
        <div className="p-6 border-b">
          <h3 className="text-lg font-semibold">最近活动</h3>
        </div>
        <div className="p-6">
          <div className="space-y-4">
            {[
              { action: '查询ASIN数据', asin: 'B0891XFDJ2', time: '2分钟前' },
              { action: '导出Excel报告', asin: 'B08EXAMPLE1', time: '15分钟前' },
              { action: '批量查询', asin: '多个ASIN', time: '1小时前' },
              { action: '性能统计查看', asin: '-', time: '2小时前' },
            ].map((item, index) => (
              <div key={index} className="flex items-center justify-between py-2">
                <div>
                  <p className="font-medium">{item.action}</p>
                  <p className="text-sm text-muted-foreground">ASIN: {item.asin}</p>
                </div>
                <p className="text-sm text-muted-foreground">{item.time}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
