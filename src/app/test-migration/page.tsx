'use client';

import { useState } from 'react';

export default function TestMigrationPage() {
  const [result, setResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const testSingleAsin = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await fetch('/api/sjf/list', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          country: 'CA',
          asin: 'B0891XFDJ2',
          format: 'json'
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log('API Response:', data); // 添加调试日志
      setResult(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  const testBatchAsin = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await fetch('/api/sjf/list', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          country: 'CA',
          asin: ['B0891XFDJ2', 'B08EXAMPLE1'],
          format: 'json'
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      setResult(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  const testStats = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await fetch('/api/sjf/stats');

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      setResult(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  const testRawApi = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await fetch('/api/list', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          country: 'CA',
          asin: 'B0891XFDJ2'
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log('Raw API Response:', data);
      setResult(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div>
        <h2 className="text-2xl font-bold tracking-tight">服务层迁移测试</h2>
        <p className="text-muted-foreground">
          测试从Express迁移到Next.js的API功能
        </p>
      </div>

      {/* Test Controls */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <button
          onClick={testSingleAsin}
          disabled={loading}
          className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-3 px-4 rounded-lg disabled:opacity-50 transition-colors"
        >
          {loading ? '测试中...' : '测试单个ASIN'}
        </button>

        <button
          onClick={testBatchAsin}
          disabled={loading}
          className="bg-green-500 hover:bg-green-700 text-white font-bold py-3 px-4 rounded-lg disabled:opacity-50 transition-colors"
        >
          {loading ? '测试中...' : '测试批量ASIN'}
        </button>

        <button
          onClick={testStats}
          disabled={loading}
          className="bg-purple-500 hover:bg-purple-700 text-white font-bold py-3 px-4 rounded-lg disabled:opacity-50 transition-colors"
        >
          {loading ? '测试中...' : '测试性能统计'}
        </button>

        <button
          onClick={testRawApi}
          disabled={loading}
          className="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-3 px-4 rounded-lg disabled:opacity-50 transition-colors"
        >
          {loading ? '测试中...' : '测试原始API'}
        </button>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-destructive/15 border border-destructive/20 text-destructive px-4 py-3 rounded-lg">
          <div className="flex items-center gap-2">
            <strong>错误:</strong>
            <span>{error}</span>
          </div>
        </div>
      )}

      {/* Results Display */}
      {result && (
        <div className="bg-card border rounded-lg">
          <div className="p-4 border-b">
            <h3 className="text-lg font-semibold">测试结果</h3>
          </div>
          <div className="p-4">
            <pre className="bg-muted p-4 rounded-lg overflow-auto max-h-96 text-sm">
              {JSON.stringify(result, null, 2)}
            </pre>
          </div>
        </div>
      )}
    </div>
  );
}
