"use client"

import * as React from "react"
import { CalendarIcon } from "lucide-react"
import { DayPicker } from "react-day-picker"
import { format } from "date-fns"
import { zhCN } from "date-fns/locale"

import { cn } from "@/lib/utils"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"

interface DatePickerProps {
  value?: string
  onChange?: (date: string) => void
  placeholder?: string
  className?: string
  disabled?: boolean
}

export function DatePicker({
  value,
  onChange,
  placeholder = "选择日期",
  className,
  disabled = false,
}: DatePickerProps) {
  const [open, setOpen] = React.useState(false)
  const [selectedDate, setSelectedDate] = React.useState<Date | undefined>(
    value ? new Date(value) : undefined
  )

  const handleDateSelect = (date: Date | undefined) => {
    setSelectedDate(date)
    if (date) {
      const dateString = format(date, "yyyy-MM-dd")
      onChange?.(dateString)
    }
    setOpen(false)
  }

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          className={cn(
            "w-full justify-start text-left font-normal h-11 border-2 border-gray-200 hover:border-blue-400 focus:border-blue-400 focus:ring-4 focus:ring-blue-400/10 transition-all duration-200",
            !selectedDate && "text-gray-500",
            selectedDate && "text-gray-800 border-blue-300",
            className
          )}
          disabled={disabled}
        >
          <CalendarIcon className="mr-3 h-5 w-5 text-blue-500" />
          {selectedDate ? format(selectedDate, "yyyy年MM月dd日", { locale: zhCN }) : placeholder}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0 shadow-xl border-0" align="start">
        <div className="bg-gradient-to-br from-blue-50 to-indigo-50 p-4 rounded-t-xl">
          <h4 className="font-bold text-base text-gray-800 mb-1 flex items-center gap-2">
            <CalendarIcon className="h-4 w-4 text-blue-500" />
            选择查询日期
          </h4>
        </div>
        <div className="p-4 bg-white rounded-b-xl">
          <DayPicker
            mode="single"
            selected={selectedDate}
            onSelect={handleDateSelect}
            locale={zhCN}
            className="rdp-custom"
            classNames={{
              months: "flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",
              month: "space-y-4",
              caption: "flex justify-center pt-1 relative items-center",
              caption_label: "text-sm font-medium text-gray-900",
              nav: "space-x-1 flex items-center",
              nav_button: cn(
                "inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors",
                "h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100 hover:bg-blue-100"
              ),
              nav_button_previous: "absolute left-1",
              nav_button_next: "absolute right-1",
              table: "w-full border-collapse space-y-1",
              head_row: "flex",
              head_cell: "text-gray-500 rounded-md w-8 font-normal text-[0.8rem]",
              row: "flex w-full mt-2",
              cell: cn(
                "relative p-0 text-center text-sm focus-within:relative focus-within:z-20 [&:has([aria-selected])]:bg-blue-100 [&:has([aria-selected].day-outside)]:bg-blue-50/50 [&:has([aria-selected].day-range-end)]:rounded-r-md",
                "first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md"
              ),
              day: cn(
                "inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors",
                "h-8 w-8 p-0 font-normal aria-selected:opacity-100 hover:bg-blue-100 hover:text-blue-900"
              ),
              day_selected: "bg-blue-500 text-white hover:bg-blue-600 hover:text-white focus:bg-blue-600 focus:text-white",
              day_today: "bg-blue-100 text-blue-900 font-semibold",
              day_outside: "text-gray-400 opacity-50 aria-selected:bg-blue-100/50 aria-selected:text-gray-500 aria-selected:opacity-30",
              day_disabled: "text-gray-400 opacity-50",
              day_range_middle: "aria-selected:bg-blue-100 aria-selected:text-blue-900",
              day_hidden: "invisible",
            }}
            components={{
              IconLeft: ({ ...props }) => <span className="h-4 w-4">‹</span>,
              IconRight: ({ ...props }) => <span className="h-4 w-4">›</span>,
            }}
          />
        </div>
      </PopoverContent>
    </Popover>
  )
}
