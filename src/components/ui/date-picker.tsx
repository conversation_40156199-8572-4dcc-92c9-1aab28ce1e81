"use client"

import * as React from "react"
import { CalendarIcon } from "lucide-react"
import dayjs from "dayjs"
import "dayjs/locale/zh-cn"

import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"

interface DatePickerProps {
  value?: string
  onChange?: (date: string) => void
  placeholder?: string
  className?: string
  disabled?: boolean
}

export function DatePicker({
  value,
  onChange,
  placeholder = "选择日期",
  className,
  disabled = false,
}: DatePickerProps) {
  const [open, setOpen] = React.useState(false)
  const [selectedDate, setSelectedDate] = React.useState<string>(value || "")

  // 设置中文语言环境
  React.useEffect(() => {
    dayjs.locale('zh-cn')
  }, [])

  // 生成日期选项（最近30天）
  const generateDateOptions = () => {
    const dates = []
    const today = dayjs()
    
    for (let i = 0; i < 30; i++) {
      const date = today.subtract(i, 'day')
      dates.push({
        value: date.format('YYYY-MM-DD'),
        label: date.format('YYYY年MM月DD日'),
        display: date.format('MM-DD')
      })
    }
    
    return dates
  }

  const dateOptions = generateDateOptions()

  const handleDateSelect = (dateValue: string) => {
    setSelectedDate(dateValue)
    onChange?.(dateValue)
    setOpen(false)
  }

  const selectedDateLabel = selectedDate 
    ? dateOptions.find(d => d.value === selectedDate)?.label 
    : null

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          className={cn(
            "w-full justify-start text-left font-normal h-11 border-2 border-gray-200 hover:border-blue-400 focus:border-blue-400 focus:ring-4 focus:ring-blue-400/10 transition-all duration-200",
            !selectedDate && "text-gray-500",
            selectedDate && "text-gray-800 border-blue-300",
            className
          )}
          disabled={disabled}
        >
          <CalendarIcon className="mr-3 h-5 w-5 text-blue-500" />
          {selectedDateLabel || placeholder}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80 p-0 shadow-xl border-0" align="start">
        <div className="bg-gradient-to-br from-blue-50 to-indigo-50 p-6 rounded-t-xl">
          <h4 className="font-bold text-lg text-gray-800 mb-2 flex items-center gap-2">
            <CalendarIcon className="h-5 w-5 text-blue-500" />
            选择查询日期
          </h4>
          <p className="text-sm text-gray-600">选择您要查询的数据日期</p>
        </div>
        <div className="p-4 bg-white rounded-b-xl">
          <div className="grid grid-cols-2 gap-2 max-h-72 overflow-y-auto">
            {dateOptions.map((date) => (
              <Button
                key={date.value}
                variant="ghost"
                className={cn(
                  "justify-start text-sm h-12 rounded-xl transition-all duration-200",
                  selectedDate === date.value
                    ? "bg-gradient-to-r from-blue-500 to-indigo-500 text-white hover:from-blue-600 hover:to-indigo-600 shadow-lg"
                    : "hover:bg-blue-50 hover:text-blue-700 border border-gray-100 hover:border-blue-200"
                )}
                onClick={() => handleDateSelect(date.value)}
              >
                <div className="flex flex-col items-start w-full">
                  <span className="font-semibold">{date.display}</span>
                  <span className="text-xs opacity-75">
                    {dayjs(date.value).format('dddd')}
                  </span>
                </div>
              </Button>
            ))}
          </div>
        </div>
      </PopoverContent>
    </Popover>
  )
}
