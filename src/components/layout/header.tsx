'use client';

import React from 'react';
import { usePathname } from 'next/navigation';
import { findNavigationItem } from '@/config/navigation';
import { Button } from '@/components/ui/button';
import { Bell, Search, User } from 'lucide-react';

export function Header() {
  const pathname = usePathname();
  const currentItem = findNavigationItem(pathname);

  return (
    <header className="h-16 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="flex items-center justify-between h-full px-6 md:px-8">
        {/* Left side - Page title and breadcrumb */}
        <div className="flex items-center gap-4">
          <div className="ml-10 md:ml-0"> {/* Add margin for mobile menu button */}
            <h1 className="text-lg font-semibold">
              {currentItem?.label || '页面'}
            </h1>
            {currentItem?.description && (
              <p className="text-sm text-muted-foreground">
                {currentItem.description}
              </p>
            )}
          </div>
        </div>

        {/* Right side - Actions */}
        <div className="flex items-center gap-2">
          {/* Search button */}
          <Button variant="ghost" size="sm" className="hidden md:flex">
            <Search className="h-4 w-4" />
          </Button>

          {/* Notifications */}
          <Button variant="ghost" size="sm" className="relative">
            <Bell className="h-4 w-4" />
            <span className="absolute -top-1 -right-1 h-2 w-2 bg-red-500 rounded-full"></span>
          </Button>

          {/* User menu */}
          <Button variant="ghost" size="sm">
            <User className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </header>
  );
}
