# 🔄 多ASIN查询功能实现说明

## 🎯 功能概述

成功实现了多ASIN查询功能，支持单个和批量ASIN数据查询，根据ASIN数量自动选择不同的处理方式：
- **单个ASIN**: 在页面显示数据表格
- **多个ASIN**: 自动下载Excel文件

## 🔧 技术实现

### 核心逻辑
```typescript
const isMultipleAsins = asins.length > 1;

if (isMultipleAsins) {
  // 多个ASIN：下载Excel文件
  // format: 'excel'
} else {
  // 单个ASIN：显示在页面上
  // format: 'json'
}
```

### API调用差异

#### **单个ASIN查询**
```typescript
const response = await fetch("/api/list", {
  method: "POST",
  headers: { "Content-Type": "application/json" },
  body: JSON.stringify({
    asin: asins[0],        // 字符串
    date: dateValue,
    format: 'json'         // 返回JSON数据
  }),
});

// 处理JSON响应
const { data } = await response.json();
setList(data);  // 显示在表格中
```

#### **多个ASIN查询**
```typescript
const response = await fetch("/api/list", {
  method: "POST",
  headers: { "Content-Type": "application/json" },
  body: JSON.stringify({
    asin: asins,           // 数组
    date: dateValue,
    format: 'excel'        // 返回Excel文件
  }),
});

// 处理Excel文件下载
const blob = await response.blob();
// 自动下载文件
```

## 🎨 用户界面改进

### 动态按钮文本
```typescript
{(() => {
  const asins = processAsinInput(asinValue);
  if (asins.length > 1) {
    return `查询并下载 ${asins.length} 个ASIN数据`;
  } else if (asins.length === 1) {
    return '查询ASIN数据';
  } else {
    return '开始查询';
  }
})()}
```

### 用户提示信息
```jsx
<div>
  <p>支持单个ASIN或多个ASIN（换行、逗号或空格分隔）</p>
  <p className="text-xs text-gray-400 mt-1">
    • 单个ASIN：在页面显示数据表格
    • 多个ASIN：自动下载Excel文件
  </p>
</div>
```

### 成功消息显示
```jsx
{successMessage && (
  <div className="bg-green-50 border border-green-200 rounded-lg p-4">
    <div className="w-5 h-5 bg-green-500 rounded-full">
      <span className="text-white text-xs">✓</span>
    </div>
    <div>
      <p className="text-green-800 font-medium">下载成功</p>
      <p className="text-green-600 text-sm">{successMessage}</p>
    </div>
    <button onClick={() => setSuccessMessage(null)}>×</button>
  </div>
)}
```

## 📁 文件下载处理

### 自动下载逻辑
```typescript
// 处理Excel文件下载
const blob = await response.blob();
const url = window.URL.createObjectURL(blob);
const a = document.createElement('a');
a.style.display = 'none';
a.href = url;

// 从响应头获取文件名
const contentDisposition = response.headers.get('Content-Disposition');
let filename = `asin-data-${new Date().toISOString().slice(0, 10)}.xlsx`;
if (contentDisposition) {
  const filenameMatch = contentDisposition.match(/filename="(.+)"/);
  if (filenameMatch) {
    filename = filenameMatch[1];
  }
}

a.download = filename;
document.body.appendChild(a);
a.click();
window.URL.revokeObjectURL(url);
document.body.removeChild(a);
```

### 文件名格式
```
asin-data-{country}-{timestamp}.xlsx
例如: asin-data-CA-2025-06-19-14-30-45.xlsx
```

## 🔄 输入处理

### ASIN解析函数
```typescript
const processAsinInput = (input: string) => {
  if (!input.trim()) return [];

  // 分割ASIN，支持换行、逗号、空格等分隔符
  const asins = input
    .split(/[\n,\s]+/)
    .map(asin => asin.trim())
    .filter(asin => asin.length > 0);

  return asins;
};
```

### 支持的分隔符
- **换行符**: `\n`
- **逗号**: `,`
- **空格**: ` `
- **组合使用**: 可以混合使用多种分隔符

### 输入示例
```
B0891XFDJ2
B0891VDLV5, B08V8T5JWT
B0891XFDJ2 B0891VDLV5
B08V8T5JWT,B0891XFDJ2,B0891VDLV5
```

## 🚀 用户体验优化

### 状态管理
```typescript
const [successMessage, setSuccessMessage] = useState<string | null>(null);

// 清理状态
const initial = async () => {
  setError(null);
  setSuccessMessage(null);
  // ...
};
```

### 反馈机制
1. **加载状态**: 显示加载动画和"正在查询数据..."
2. **成功提示**: 绿色成功消息，显示下载的文件信息
3. **错误处理**: 红色错误消息，显示具体错误信息
4. **动态按钮**: 根据输入的ASIN数量显示不同文本

### 交互流程

#### **单个ASIN流程**
1. 输入单个ASIN
2. 按钮显示"查询ASIN数据"
3. 点击查询
4. 数据显示在页面表格中
5. 显示统计卡片

#### **多个ASIN流程**
1. 输入多个ASIN（用分隔符分开）
2. 按钮显示"查询并下载 X 个ASIN数据"
3. 点击查询
4. 自动下载Excel文件
5. 显示绿色成功消息
6. 清空表格数据

## 📊 API后端支持

### 路由逻辑
- **检测ASIN类型**: `Array.isArray(asin)`
- **默认格式**: 批量请求默认Excel，单个请求默认JSON
- **Excel生成**: 使用`ExcelProcessingService.createAsinDataExcel()`
- **文件响应**: 设置正确的Content-Type和Content-Disposition头

### 性能优化
- **批量处理**: `SjfService.getBatchAsinHistory()`
- **并发请求**: 同时处理多个ASIN
- **缓存机制**: 避免重复请求
- **错误处理**: 部分失败时的优雅降级

## 🎉 实现效果

新的多ASIN查询功能提供了：

- 📊 **智能识别**: 自动识别单个/多个ASIN
- 📁 **自动下载**: 多个ASIN自动生成Excel文件
- 🎨 **动态界面**: 按钮文本根据输入动态变化
- ✅ **用户反馈**: 清晰的成功/错误提示
- 🔄 **灵活输入**: 支持多种分隔符格式
- 📱 **响应式**: 适配各种屏幕尺寸

这个实现完美地满足了用户对批量ASIN查询的需求，提供了专业的数据分析体验！
