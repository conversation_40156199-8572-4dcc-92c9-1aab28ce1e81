{"name": "express-node", "version": "1.0.0", "main": "dist/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "tsc", "start": "node dist/index.js", "dev": "nodemon src/index.ts"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@types/express": "^5.0.3", "@types/multer": "^1.4.13", "bull": "^4.12.2", "dayjs": "^1.11.13", "exceljs": "^4.4.0", "express": "^5.1.0", "ioredis": "^5.3.2", "multer": "^2.0.1", "uuid": "^9.0.1", "xlsx": "^0.18.5"}, "packageManager": "pnpm@9.6.0+sha512.38dc6fba8dba35b39340b9700112c2fe1e12f10b17134715a4aa98ccf7bb035e76fd981cf0bb384dfa98f8d6af5481c2bef2f4266a24bfa20c34eb7147ce0b5e", "devDependencies": {"@types/bull": "^4.10.0", "@types/exceljs": "^1.3.2", "@types/express-serve-static-core": "^5.0.6", "@types/node": "^24.0.1", "@types/uuid": "^9.0.8", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}