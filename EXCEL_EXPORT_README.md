# Excel导出功能使用说明

## 功能概述

您的SJF API现在支持智能格式选择：
- **批量ASIN请求**：默认返回Excel文件，每个ASIN作为单独的工作表
- **单个ASIN请求**：默认返回JSON格式，保持向后兼容

## API使用方法

### 批量请求（默认Excel格式）

```http
POST /api/sjf/list
Content-Type: application/json

{
  "country": "CA",
  "asin": ["B0891XFDJ2", "B08EXAMPLE1", "B08EXAMPLE2"],
  "date": "2024-01-01" // 可选
}
```
**结果**: 自动下载Excel文件

### 批量请求（指定JSON格式）

```http
POST /api/sjf/list
Content-Type: application/json

{
  "country": "CA",
  "asin": ["B0891XFDJ2", "B08EXAMPLE1", "B08EXAMPLE2"],
  "format": "json",
  "date": "2024-01-01" // 可选
}
```
**结果**: 返回JSON数据

### 参数说明

- `country`: 国家代码（必需）
- `asin`: ASIN或ASIN数组（必需）
- `format`: 响应格式，可选值：
  - `"json"`: 返回JSON格式
  - `"excel"`: 返回Excel文件
  - **默认行为**: 批量请求=Excel，单个请求=JSON
- `date`: 可选的日期过滤参数

### 响应格式

#### Excel格式响应
- **Content-Type**: `application/vnd.openxmlformats-officedocument.spreadsheetml.sheet`
- **Content-Disposition**: `attachment; filename="asin_batch_data_YYYYMMDDHHMMSS.xlsx"`
- **响应体**: Excel文件的二进制数据

#### JSON格式响应（默认）
```json
{
  "data": [
    {
      "asin": "B0891XFDJ2",
      "data": [
        {
          "dates": "2024-01-01",
          "buyboxPrice": 29.99,
          "category1": 1500,
          "category2": 2000
        }
      ]
    }
  ],
  "meta": {
    "totalRequests": 3,
    "successfulRequests": 2,
    "processingTime": 1250
  }
}
```

## Excel文件结构

### 工作表组织
- 每个ASIN创建一个独立的工作表
- 工作表名称 = ASIN值
- 如果某个ASIN没有数据，工作表中显示"No data available for this ASIN"

### 数据格式
- 第一行：列标题（加粗，灰色背景）
- 后续行：数据行
- 数字列：自动格式化为适当的小数位数
- 列宽：自动调整（最小10，最大50字符）

### 样式特性
- 表头：粗体字，灰色背景
- 边框：所有数据单元格都有细边框
- 列宽：根据内容自动调整

## 使用示例

### JavaScript/Node.js
```javascript
const axios = require('axios');
const fs = require('fs');

async function downloadExcel() {
  try {
    const response = await axios.post('http://localhost:3000/api/sjf/list', {
      country: 'CA',
      asin: ['B0891XFDJ2', 'B08EXAMPLE1'],
      format: 'excel'
    }, {
      responseType: 'arraybuffer'
    });

    fs.writeFileSync('asin_data.xlsx', Buffer.from(response.data));
    console.log('Excel文件已保存');
  } catch (error) {
    console.error('下载失败:', error.message);
  }
}
```

### cURL
```bash
curl -X POST http://localhost:3000/api/sjf/list \
  -H "Content-Type: application/json" \
  -d '{
    "country": "CA",
    "asin": ["B0891XFDJ2", "B08EXAMPLE1"],
    "format": "excel"
  }' \
  --output asin_data.xlsx
```

### Python
```python
import requests

response = requests.post('http://localhost:3000/api/sjf/list', json={
    'country': 'CA',
    'asin': ['B0891XFDJ2', 'B08EXAMPLE1'],
    'format': 'excel'
})

with open('asin_data.xlsx', 'wb') as f:
    f.write(response.content)
```

## 性能特性

### 优化功能
- **缓存机制**: 相同请求5分钟内直接返回缓存
- **并发控制**: 最多5个并发请求
- **超时控制**: 10秒请求超时
- **性能监控**: 自动记录处理时间

### 性能对比
- JSON响应：通常更快，适合程序处理
- Excel响应：增加约100-500ms处理时间，适合人工查看

## 错误处理

### 常见错误
1. **400 Bad Request**: 缺少必需参数或格式无效
2. **422 Unprocessable Entity**: API返回非成功状态
3. **500 Internal Server Error**: Excel生成失败
4. **502 Bad Gateway**: 上游API请求失败

### 错误响应示例
```json
{
  "error": "Invalid format parameter. Must be 'json' or 'excel'",
  "details": "..."
}
```

## 测试

运行测试脚本验证功能：
```bash
node test_excel_export.js
```

测试包括：
- JSON格式响应测试
- Excel导出功能测试
- 单个ASIN Excel导出
- 无效参数处理
- 性能对比测试

## 注意事项

1. **单个ASIN**: 即使指定`format: "excel"`，单个ASIN请求仍返回JSON格式（向后兼容）
2. **文件大小**: Excel文件大小取决于数据量，通常比JSON格式大20-50%
3. **浏览器下载**: 浏览器会自动触发文件下载
4. **缓存**: Excel和JSON请求共享相同的缓存机制

## 技术实现

- **Excel库**: ExcelJS
- **文件格式**: .xlsx (Office Open XML)
- **内存处理**: 直接在内存中生成，无临时文件
- **流式传输**: 支持大文件的流式下载
